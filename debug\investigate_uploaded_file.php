<?php
/**
 * Investigate Uploaded File Issues
 * Check database records and file paths for the uploaded file
 */

require_once '../api/config/config.php';

echo "<h1>Uploaded File Investigation</h1>\n";
echo "<pre>\n";

try {
    // Connect to database
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "✅ Database connection successful\n\n";
    
    // Get the most recent uploaded file (likely the one from Android)
    echo "=== RECENT UPLOADED FILES ===\n";
    $stmt = $pdo->query("
        SELECT 
            d.*,
            u.name as user_name,
            u.email as user_email
        FROM documents d
        LEFT JOIN users u ON d.user_id = u.id
        ORDER BY d.created_at DESC
        LIMIT 5
    ");
    
    $files = $stmt->fetchAll();
    
    if (empty($files)) {
        echo "❌ No files found in database\n";
        exit();
    }
    
    echo "Found " . count($files) . " recent files:\n\n";
    
    foreach ($files as $index => $file) {
        echo "--- FILE " . ($index + 1) . " ---\n";
        echo "ID: " . $file['id'] . "\n";
        echo "Original Name: " . $file['original_name'] . "\n";
        echo "File Name: " . $file['file_name'] . "\n";
        echo "Storage Path: " . $file['storage_path'] . "\n";
        echo "File Size: " . $file['file_size'] . " bytes\n";
        echo "File Type: " . $file['file_type'] . "\n";
        echo "File Hash: " . $file['file_hash'] . "\n";
        echo "User: " . $file['user_name'] . " (" . $file['user_email'] . ")\n";
        echo "Created: " . $file['created_at'] . "\n";
        echo "Updated: " . $file['updated_at'] . "\n";
        
        // Check if this looks like the Android uploaded file
        if (strpos($file['id'], 'xlz--lI_tzVN-VPVWFdx') !== false || 
            strpos($file['original_name'], 'test') !== false ||
            $index == 0) { // Most recent file
            
            echo "\n🔍 DETAILED ANALYSIS OF THIS FILE:\n";
            
            // 1. Analyze ID format
            echo "\n1. ID FORMAT ANALYSIS:\n";
            echo "   ID: '" . $file['id'] . "'\n";
            echo "   Length: " . strlen($file['id']) . " characters\n";
            echo "   Contains hyphens: " . (strpos($file['id'], '-') !== false ? 'YES' : 'NO') . "\n";
            echo "   Contains underscores: " . (strpos($file['id'], '_') !== false ? 'YES' : 'NO') . "\n";
            
            // Check if it matches Firebase push key pattern
            $firebase_chars = '-0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz';
            $valid_firebase_id = true;
            for ($i = 0; $i < strlen($file['id']); $i++) {
                if (strpos($firebase_chars, $file['id'][$i]) === false) {
                    $valid_firebase_id = false;
                    break;
                }
            }
            echo "   Valid Firebase push key format: " . ($valid_firebase_id ? 'YES' : 'NO') . "\n";
            
            // 2. Check file paths
            echo "\n2. FILE PATH ANALYSIS:\n";
            echo "   Storage Path (DB): " . $file['storage_path'] . "\n";
            
            // Check different possible file locations
            $possible_paths = [
                '../uploads/' . $file['storage_path'],
                '../uploads/pdfs/' . $file['file_name'],
                'uploads/' . $file['storage_path'],
                'uploads/pdfs/' . $file['file_name'],
                '../uploads/pdfs/' . $file['storage_path'],
                UPLOAD_DIR . $file['storage_path'],
                UPLOAD_DIR . 'pdfs/' . $file['file_name']
            ];
            
            echo "   Checking possible file locations:\n";
            foreach ($possible_paths as $path) {
                $exists = file_exists($path);
                $size = $exists ? filesize($path) : 0;
                echo "     - $path: " . ($exists ? "✅ EXISTS ($size bytes)" : "❌ NOT FOUND") . "\n";
            }
            
            // 3. Admin panel path check
            echo "\n3. ADMIN PANEL PATH CHECK:\n";
            $admin_expected_path = '../uploads/' . $file['storage_path'];
            echo "   Admin panel expects: $admin_expected_path\n";
            echo "   File exists there: " . (file_exists($admin_expected_path) ? 'YES' : 'NO') . "\n";
            
            // 4. Check uploads directory structure
            echo "\n4. UPLOADS DIRECTORY STRUCTURE:\n";
            $upload_dirs = ['../uploads/', '../uploads/pdfs/', 'uploads/', 'uploads/pdfs/'];
            foreach ($upload_dirs as $dir) {
                if (is_dir($dir)) {
                    echo "   Directory: $dir\n";
                    $files_in_dir = scandir($dir);
                    foreach ($files_in_dir as $f) {
                        if ($f !== '.' && $f !== '..' && is_file($dir . $f)) {
                            $file_size = filesize($dir . $f);
                            echo "     - $f ($file_size bytes)\n";
                            
                            // Check if this matches our file
                            if ($f === $file['file_name'] || $f === basename($file['storage_path'])) {
                                echo "       ⭐ THIS MATCHES OUR FILE!\n";
                            }
                        }
                    }
                } else {
                    echo "   Directory: $dir - NOT FOUND\n";
                }
            }
            
            // 5. Generate test Firebase ID for comparison
            echo "\n5. FIREBASE ID GENERATION TEST:\n";
            $test_id = generatePushKey();
            echo "   Sample generated ID: $test_id\n";
            echo "   Sample ID length: " . strlen($test_id) . " characters\n";
            echo "   Matches pattern: " . (strlen($test_id) == 20 ? 'YES' : 'NO') . "\n";
            
            break; // Only analyze the first/most relevant file
        }
        echo "\n";
    }
    
    // Check storage usage
    echo "\n=== STORAGE USAGE ===\n";
    $stmt = $pdo->query("SELECT * FROM storage_usage ORDER BY last_updated DESC");
    $storage_records = $stmt->fetchAll();
    
    foreach ($storage_records as $storage) {
        echo "User ID: " . $storage['user_id'] . "\n";
        echo "Total Used: " . $storage['total_used'] . " bytes (" . round($storage['total_used'] / 1024 / 1024, 2) . " MB)\n";
        echo "Document Count: " . $storage['document_count'] . "\n";
        echo "Last Updated: " . $storage['last_updated'] . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
