<?php
/**
 * Comprehensive File Upload Diagnostic Script
 * Investigate why physical files aren't being saved despite successful database records
 */

require_once '../api/config/config.php';

echo "<h1>File Upload Issue Diagnostic</h1>\n";
echo "<pre>\n";

echo "=== SYSTEM ENVIRONMENT ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Current working directory: " . getcwd() . "\n";
echo "Script location: " . __FILE__ . "\n";
echo "Web server user: " . get_current_user() . "\n";
echo "Process owner: " . posix_getpwuid(posix_geteuid())['name'] . "\n\n";

echo "=== CONFIGURATION ANALYSIS ===\n";
echo "UPLOAD_DIR constant: " . UPLOAD_DIR . "\n";
echo "MAX_FILE_SIZE: " . MAX_FILE_SIZE . " bytes (" . (MAX_FILE_SIZE / 1024 / 1024) . " MB)\n";
echo "ALLOWED_FILE_TYPES: " . implode(', ', ALLOWED_FILE_TYPES) . "\n\n";

echo "=== PHP UPLOAD SETTINGS ===\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "file_uploads: " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "\n";
echo "upload_tmp_dir: " . (ini_get('upload_tmp_dir') ?: 'Default') . "\n\n";

echo "=== DIRECTORY ANALYSIS ===\n";
$directories_to_check = [
    '../uploads/',
    '../uploads/pdfs/',
    'uploads/',
    'uploads/pdfs/',
    UPLOAD_DIR,
    UPLOAD_DIR . 'pdfs/'
];

foreach ($directories_to_check as $dir) {
    echo "Directory: $dir\n";
    
    if (is_dir($dir)) {
        echo "  ✅ Exists: YES\n";
        echo "  Real path: " . realpath($dir) . "\n";
        echo "  Permissions: " . substr(sprintf('%o', fileperms($dir)), -4) . "\n";
        echo "  Owner: " . posix_getpwuid(fileowner($dir))['name'] . "\n";
        echo "  Group: " . posix_getgrgid(filegroup($dir))['name'] . "\n";
        echo "  Readable: " . (is_readable($dir) ? 'YES' : 'NO') . "\n";
        echo "  Writable: " . (is_writable($dir) ? 'YES' : 'NO') . "\n";
        
        // List files
        $files = scandir($dir);
        $file_count = count($files) - 2; // Exclude . and ..
        echo "  File count: $file_count\n";
        
        if ($file_count > 0 && $file_count <= 10) {
            echo "  Files:\n";
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    $file_path = $dir . $file;
                    $size = is_file($file_path) ? filesize($file_path) : 0;
                    $type = is_file($file_path) ? 'file' : 'directory';
                    echo "    - $file ($type, $size bytes)\n";
                }
            }
        }
    } else {
        echo "  ❌ Exists: NO\n";
        
        // Try to create the directory
        echo "  Attempting to create directory...\n";
        if (mkdir($dir, 0755, true)) {
            echo "  ✅ Created successfully\n";
            echo "  New permissions: " . substr(sprintf('%o', fileperms($dir)), -4) . "\n";
        } else {
            echo "  ❌ Failed to create\n";
        }
    }
    echo "\n";
}

echo "=== RECENT DATABASE RECORDS ===\n";
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "✅ Database connection successful\n\n";
    
    // Get recent files
    $stmt = $pdo->query("
        SELECT 
            id, original_name, file_name, storage_path, file_size, 
            created_at, updated_at
        FROM documents 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    $files = $stmt->fetchAll();
    
    foreach ($files as $index => $file) {
        echo "--- RECENT FILE " . ($index + 1) . " ---\n";
        echo "ID: " . $file['id'] . "\n";
        echo "Original name: " . $file['original_name'] . "\n";
        echo "File name: " . $file['file_name'] . "\n";
        echo "Storage path: " . $file['storage_path'] . "\n";
        echo "File size: " . $file['file_size'] . " bytes\n";
        echo "Created: " . $file['created_at'] . "\n";
        echo "Updated: " . $file['updated_at'] . "\n";
        
        // Check if physical file exists
        $possible_paths = [
            '../uploads/' . $file['storage_path'],
            '../uploads/pdfs/' . $file['file_name'],
            UPLOAD_DIR . $file['storage_path'],
            UPLOAD_DIR . 'pdfs/' . $file['file_name']
        ];
        
        echo "Physical file check:\n";
        $found = false;
        foreach ($possible_paths as $path) {
            $exists = file_exists($path);
            echo "  $path: " . ($exists ? '✅ EXISTS' : '❌ MISSING') . "\n";
            if ($exists) {
                echo "    Size: " . filesize($path) . " bytes\n";
                echo "    Modified: " . date('Y-m-d H:i:s', filemtime($path)) . "\n";
                $found = true;
            }
        }
        
        if (!$found) {
            echo "  ⚠️  FILE NOT FOUND ANYWHERE!\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "=== UPLOAD PROCESS SIMULATION ===\n";
echo "Simulating the upload process to identify potential issues...\n\n";

// Test directory creation
$test_upload_dir = UPLOAD_DIR . 'pdfs/';
echo "Target upload directory: $test_upload_dir\n";

if (!is_dir($test_upload_dir)) {
    echo "Directory doesn't exist, attempting to create...\n";
    if (mkdir($test_upload_dir, 0755, true)) {
        echo "✅ Directory created successfully\n";
    } else {
        echo "❌ Failed to create directory\n";
        echo "Error: " . error_get_last()['message'] . "\n";
    }
} else {
    echo "✅ Directory exists\n";
}

// Test file write permissions
$test_file_path = $test_upload_dir . 'test_write_' . time() . '.txt';
echo "\nTesting write permissions with: $test_file_path\n";

$test_content = "Test file created at " . date('Y-m-d H:i:s');
if (file_put_contents($test_file_path, $test_content)) {
    echo "✅ Write test successful\n";
    echo "File size: " . filesize($test_file_path) . " bytes\n";
    
    // Clean up test file
    if (unlink($test_file_path)) {
        echo "✅ Test file cleaned up\n";
    } else {
        echo "⚠️  Could not delete test file\n";
    }
} else {
    echo "❌ Write test failed\n";
    echo "Error: " . error_get_last()['message'] . "\n";
}

echo "\n=== RECOMMENDATIONS ===\n";
echo "Based on the analysis above:\n";
echo "1. Check directory permissions (should be 755 or 777)\n";
echo "2. Verify web server has write access to uploads directory\n";
echo "3. Check if move_uploaded_file() is failing silently\n";
echo "4. Review PHP error logs for upload-related errors\n";
echo "5. Test with enhanced logging in upload.php\n";

echo "\n=== NEXT STEPS ===\n";
echo "1. Run enhanced upload test with logging\n";
echo "2. Check PHP error logs: " . ini_get('error_log') . "\n";
echo "3. Test upload.php with detailed debugging\n";

echo "</pre>";
?>
