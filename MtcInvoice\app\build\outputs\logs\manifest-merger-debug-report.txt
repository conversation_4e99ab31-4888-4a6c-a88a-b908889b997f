-- Merging decision tree log ---
manifest
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-73:12
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-73:12
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-73:12
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-73:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fb7b29e50de13de6699bae03e134350\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\532ab88c9a5a7c9a5425e085495f66b4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ac125b8c883ab90738f9c77c674191aa\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a5a0edb80c9262856ab2dc83becb66d\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c3653e197a0d9ce7b1c81bfe1c9e716d\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b00d0dd6e969cce4c9fa6f8a28fc759\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c3a71c986434956e93794e2fdd4d56e9\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\845141bbe0cf3614ebb31e1582629cb5\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\580fd08e32d2dfcb6516a19966132b9a\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\98e7087b778494581f3a78c60722b74d\transformed\jetified-activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c17f0349fb2b71950733d66391da1bfd\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b14e1a8ffe892cbb0e23ba1d7fea341\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\27de523b721567eef9327e739e92382a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb2887ee5e503eef507202624c20fef8\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ee8707566c9e8d986e1045c4624cab6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\04614bd7825180693c92460dbe2f1343\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e14475a7554fe4ae78ca189d84c62904\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\86af0f5221c861667b563130c2206232\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fedac3c7e8c41dabcc628c696c2db172\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd9cce39c201c4375399bcc09c04a931\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7728a0e35cc184dab7367432d42287e2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2272cfa3e774605bc79a155ae137b81\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7795f726f45554a3ba06b7b7c98bea10\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\536e0393288f21eaf4648ed7fea88f25\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\801cad532f30c3f99d278914c382cb46\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\39c4e851cece428b11a541e7845f72cd\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b144e06d21945056626210ea79265521\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aac83303acbd54d6faa4403b30e57443\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0e32fd20ed5e0bfc42e9de0931970536\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.github.zcweng:switch-button:0.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0f3f0e33c9f343fd53dd768f860fccfd\transformed\jetified-switch-button-0.0.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a0d88651a1956dbea84c323ad5e39415\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1d9d1d0306c70d236e99fdcea9287bc4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cd914daa7235223b1a773117f9a335dd\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16e0f5e986667dc08c06cf609de6b2dd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\13d501d13d38c1fa3d79a8b032ad8f8b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4117e9e5fcf7c4a7cb8090c41d53c13d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\30af0f28fd43c5c4eb668fc7c6152c9e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\489e0e29c23e2fe4fcb9f17f1889932f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ff7eeb0db705c11560ee5aa5bb87c617\transformed\exifinterface-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c5db7912cc3fa14bc630552002222a9e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f55091e88588143cbb15c0409cb153c8\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e1c84f42d4f7ed0b2549ba84527f2825\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ca31da2932cb23fecf77e89722ca0aec\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8c48121da4d8e43a232d9dafacaa2e7a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:7:5-9:40
	tools:ignore
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:9:9-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:8:9-66
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:5-80
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:5-76
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:22-73
application
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-71:19
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-71:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fb7b29e50de13de6699bae03e134350\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fb7b29e50de13de6699bae03e134350\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\532ab88c9a5a7c9a5425e085495f66b4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\532ab88c9a5a7c9a5425e085495f66b4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ac125b8c883ab90738f9c77c674191aa\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ac125b8c883ab90738f9c77c674191aa\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:7:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b00d0dd6e969cce4c9fa6f8a28fc759\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b00d0dd6e969cce4c9fa6f8a28fc759\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16e0f5e986667dc08c06cf609de6b2dd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16e0f5e986667dc08c06cf609de6b2dd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\30af0f28fd43c5c4eb668fc7c6152c9e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\30af0f28fd43c5c4eb668fc7c6152c9e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\489e0e29c23e2fe4fcb9f17f1889932f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\489e0e29c23e2fe4fcb9f17f1889932f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:23:9-29
	android:icon
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:17:9-43
	android:allowBackup
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:22:9-48
	android:networkSecurityConfig
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:19:9-69
	android:dataExtractionRules
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:15:9-65
activity#com.official.invoicegenarator.InvoiceTwo
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:24:9-26:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:26:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:25:13-39
activity#com.official.invoicegenarator.InvoiceTraker
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:27:9-29:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:29:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:28:13-42
activity#com.official.invoicegenarator.PdfViewerActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:32:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:31:13-46
activity#com.official.invoicegenarator.DownloadListActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:33:9-35:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:35:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:34:13-49
activity#com.official.invoicegenarator.MoneyBagActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:38:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:37:13-45
activity#com.official.invoicegenarator.ProfileActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:39:9-41:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:41:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:40:13-44
activity#com.official.invoicegenarator.FingerprintSettingsActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:42:9-44:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:44:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:43:13-56
activity#com.official.invoicegenarator.VarifyActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:45:9-47:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:47:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:46:13-43
activity#com.official.invoicegenarator.WorkerAttendenceActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:48:9-50:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:49:13-53
activity#com.official.invoicegenarator.LoginActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:51:9-54:58
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:54:13-55
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:52:13-42
activity#com.official.invoicegenarator.SelectionActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:55:9-57:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:56:13-46
activity#com.official.invoicegenarator.Home
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:58:9-61:55
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:13-52
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:60:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:13-33
activity#com.official.invoicegenarator.MainActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:62:9-70:20
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:64:13-36
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:63:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:65:13-69:29
action#android.intent.action.MAIN
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:66:17-69
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:66:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:68:17-77
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:68:27-74
uses-sdk
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fb7b29e50de13de6699bae03e134350\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4fb7b29e50de13de6699bae03e134350\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\532ab88c9a5a7c9a5425e085495f66b4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\532ab88c9a5a7c9a5425e085495f66b4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ac125b8c883ab90738f9c77c674191aa\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ac125b8c883ab90738f9c77c674191aa\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a5a0edb80c9262856ab2dc83becb66d\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a5a0edb80c9262856ab2dc83becb66d\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c3653e197a0d9ce7b1c81bfe1c9e716d\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c3653e197a0d9ce7b1c81bfe1c9e716d\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b00d0dd6e969cce4c9fa6f8a28fc759\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8b00d0dd6e969cce4c9fa6f8a28fc759\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c3a71c986434956e93794e2fdd4d56e9\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c3a71c986434956e93794e2fdd4d56e9\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\845141bbe0cf3614ebb31e1582629cb5\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\845141bbe0cf3614ebb31e1582629cb5\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\580fd08e32d2dfcb6516a19966132b9a\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\580fd08e32d2dfcb6516a19966132b9a\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\98e7087b778494581f3a78c60722b74d\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\98e7087b778494581f3a78c60722b74d\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c17f0349fb2b71950733d66391da1bfd\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c17f0349fb2b71950733d66391da1bfd\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b14e1a8ffe892cbb0e23ba1d7fea341\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7b14e1a8ffe892cbb0e23ba1d7fea341\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\27de523b721567eef9327e739e92382a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\27de523b721567eef9327e739e92382a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb2887ee5e503eef507202624c20fef8\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb2887ee5e503eef507202624c20fef8\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ee8707566c9e8d986e1045c4624cab6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ee8707566c9e8d986e1045c4624cab6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\04614bd7825180693c92460dbe2f1343\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\04614bd7825180693c92460dbe2f1343\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e14475a7554fe4ae78ca189d84c62904\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e14475a7554fe4ae78ca189d84c62904\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\86af0f5221c861667b563130c2206232\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\86af0f5221c861667b563130c2206232\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fedac3c7e8c41dabcc628c696c2db172\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fedac3c7e8c41dabcc628c696c2db172\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd9cce39c201c4375399bcc09c04a931\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd9cce39c201c4375399bcc09c04a931\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7728a0e35cc184dab7367432d42287e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7728a0e35cc184dab7367432d42287e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2272cfa3e774605bc79a155ae137b81\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2272cfa3e774605bc79a155ae137b81\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7795f726f45554a3ba06b7b7c98bea10\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7795f726f45554a3ba06b7b7c98bea10\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\536e0393288f21eaf4648ed7fea88f25\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\536e0393288f21eaf4648ed7fea88f25\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\801cad532f30c3f99d278914c382cb46\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\801cad532f30c3f99d278914c382cb46\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\39c4e851cece428b11a541e7845f72cd\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\39c4e851cece428b11a541e7845f72cd\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b144e06d21945056626210ea79265521\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b144e06d21945056626210ea79265521\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aac83303acbd54d6faa4403b30e57443\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aac83303acbd54d6faa4403b30e57443\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0e32fd20ed5e0bfc42e9de0931970536\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0e32fd20ed5e0bfc42e9de0931970536\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.zcweng:switch-button:0.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0f3f0e33c9f343fd53dd768f860fccfd\transformed\jetified-switch-button-0.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.zcweng:switch-button:0.0.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0f3f0e33c9f343fd53dd768f860fccfd\transformed\jetified-switch-button-0.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a0d88651a1956dbea84c323ad5e39415\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a0d88651a1956dbea84c323ad5e39415\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1d9d1d0306c70d236e99fdcea9287bc4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1d9d1d0306c70d236e99fdcea9287bc4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cd914daa7235223b1a773117f9a335dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cd914daa7235223b1a773117f9a335dd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16e0f5e986667dc08c06cf609de6b2dd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16e0f5e986667dc08c06cf609de6b2dd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\13d501d13d38c1fa3d79a8b032ad8f8b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\13d501d13d38c1fa3d79a8b032ad8f8b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4117e9e5fcf7c4a7cb8090c41d53c13d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4117e9e5fcf7c4a7cb8090c41d53c13d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\30af0f28fd43c5c4eb668fc7c6152c9e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\30af0f28fd43c5c4eb668fc7c6152c9e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\489e0e29c23e2fe4fcb9f17f1889932f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\489e0e29c23e2fe4fcb9f17f1889932f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ff7eeb0db705c11560ee5aa5bb87c617\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ff7eeb0db705c11560ee5aa5bb87c617\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c5db7912cc3fa14bc630552002222a9e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c5db7912cc3fa14bc630552002222a9e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f55091e88588143cbb15c0409cb153c8\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f55091e88588143cbb15c0409cb153c8\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e1c84f42d4f7ed0b2549ba84527f2825\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e1c84f42d4f7ed0b2549ba84527f2825\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ca31da2932cb23fecf77e89722ca0aec\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ca31da2932cb23fecf77e89722ca0aec\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8c48121da4d8e43a232d9dafacaa2e7a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8c48121da4d8e43a232d9dafacaa2e7a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16e0f5e986667dc08c06cf609de6b2dd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16e0f5e986667dc08c06cf609de6b2dd\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
