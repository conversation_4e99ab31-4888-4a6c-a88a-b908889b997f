package com.official.invoicegenarator.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.official.invoicegenarator.R;
import com.official.invoicegenarator.models.FileInfo;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for displaying file list in RecyclerView
 */
public class FileListAdapter extends RecyclerView.Adapter<FileListAdapter.FileViewHolder> {

    private Context context;
    private List<FileInfo> files;
    private OnFileClickListener listener;
    private SimpleDateFormat dateFormat;

    public interface OnFileClickListener {
        void onFileClick(FileInfo file);
        void onFileDelete(FileInfo file);
    }

    public FileListAdapter(Context context, List<FileInfo> files, OnFileClickListener listener) {
        this.context = context;
        this.files = files;
        this.listener = listener;
        this.dateFormat = new SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault());
    }

    @NonNull
    @Override
    public FileViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_file, parent, false);
        return new FileViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FileViewHolder holder, int position) {
        FileInfo file = files.get(position);
        
        // Set file name
        holder.fileName.setText(file.getOriginalName());
        
        // Set file size
        holder.fileSize.setText(formatFileSize(file.getSize()));
        
        // Set upload date
        try {
            Date date = new Date(file.getCreatedAt());
            holder.uploadDate.setText(dateFormat.format(date));
        } catch (Exception e) {
            holder.uploadDate.setText("Unknown date");
        }
        
        // Set file icon based on extension
        String extension = getFileExtension(file.getOriginalName()).toLowerCase();
        setFileIcon(holder.fileIcon, extension);
        
        // Set click listeners
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onFileClick(file);
            }
        });
        
        holder.deleteButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onFileDelete(file);
            }
        });
    }

    @Override
    public int getItemCount() {
        return files.size();
    }

    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1);
        }
        return "";
    }

    private void setFileIcon(ImageView imageView, String extension) {
        int iconRes;
        switch (extension) {
            case "pdf":
                iconRes = R.drawable.converter_document; // You'll need to add this icon
                break;
            case "jpg":
            case "jpeg":
            case "png":
                iconRes = R.drawable.invoicelogo; // You'll need to add this icon
                break;
            case "doc":
            case "docx":
                iconRes = R.drawable.invoicelogo; // You'll need to add this icon
                break;
            default:
                iconRes = R.drawable.view; // You'll need to add this icon
                break;
        }
        imageView.setImageResource(iconRes);
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format(Locale.getDefault(), "%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format(Locale.getDefault(), "%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format(Locale.getDefault(), "%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    static class FileViewHolder extends RecyclerView.ViewHolder {
        TextView fileName;
        TextView fileSize;
        TextView uploadDate;
        ImageView fileIcon;
        ImageView deleteButton;

        public FileViewHolder(@NonNull View itemView) {
            super(itemView);
            fileName = itemView.findViewById(R.id.fileName);
            fileSize = itemView.findViewById(R.id.fileSize);
            uploadDate = itemView.findViewById(R.id.uploadDate);
            fileIcon = itemView.findViewById(R.id.fileIcon);
            deleteButton = itemView.findViewById(R.id.deleteButton);
        }
    }
}
