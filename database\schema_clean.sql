-- MtcInvoice Database Schema (Clean Version)
-- Version: 1.1
-- Description: Optimized database schema with only essential tables
-- Date: 2025-06-15

-- Create database
CREATE DATABASE IF NOT EXISTS mtcinvoice_db;
USE mtcinvoice_db;

-- ==================== CORE TABLES ====================

-- Invoice data items table (replaces Firebase Realtime Database)
CREATE TABLE `invoice_data_items` (
  `id` VARCHAR(50) NOT NULL, -- Firebase-style push key
  `description` TEXT NOT NULL,
  `location` VARCHAR(255) NOT NULL,
  `qr` VARCHAR(255) NOT NULL,
  `lpo` VARCHAR(255) NOT NULL,
  `inb` VARCHAR(255) NOT NULL,
  `amount` VARCHAR(50) NOT NULL,
  `w_a` VARCHAR(255) NOT NULL,
  `payment_status` VARCHAR(100) NOT NULL,
  `timestamp` BIGINT NOT NULL,
  `current_date_time` VARCHAR(50) DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Users table for authentication
CREATE TABLE `users` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `email` VARCHAR(100) NOT NULL UNIQUE,
  `password` VARCHAR(255) NOT NULL,
  `role` ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
  `status` TINYINT(1) NOT NULL DEFAULT 1,
  `storage_quota` BIGINT DEFAULT 10737418240, -- 10GB default storage
  `storage_used` BIGINT DEFAULT 0,
  `last_login` DATETIME DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Documents table (simplified for file storage)
CREATE TABLE `documents` (
  `id` CHAR(36) NOT NULL, -- UUID
  `user_id` INT(11) NOT NULL,
  `original_name` VARCHAR(255) NOT NULL,
  `storage_path` VARCHAR(512) NOT NULL,
  `file_name` VARCHAR(255) NOT NULL,
  `file_size` BIGINT NOT NULL, -- in bytes
  `file_type` VARCHAR(100) NOT NULL,
  `file_hash` VARCHAR(64) NOT NULL, -- SHA-256 hash for deduplication
  `description` TEXT,
  `download_count` INT DEFAULT 0,
  `last_downloaded_at` DATETIME DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_file_hash_user` (`file_hash`, `user_id`), -- Prevent duplicate uploads
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  FULLTEXT KEY `ft_search` (`original_name`, `description`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Storage usage tracking
CREATE TABLE `storage_usage` (
  `user_id` INT(11) NOT NULL,
  `total_used` BIGINT NOT NULL DEFAULT 0,
  `document_count` INT NOT NULL DEFAULT 0,
  `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ==================== INDEXES FOR PERFORMANCE ====================

-- Additional indexes for better performance
CREATE INDEX `idx_documents_user` ON `documents` (`user_id`);
CREATE INDEX `idx_documents_created` ON `documents` (`created_at`);
CREATE INDEX `idx_documents_updated` ON `documents` (`updated_at`);

-- ==================== DEFAULT DATA ====================

-- Create default admin user (password: admin123)
INSERT INTO `users` (`name`, `email`, `password`, `role`, `status`) VALUES
('Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Create default storage usage for admin
INSERT INTO `storage_usage` (`user_id`, `total_used`, `document_count`) VALUES
(1, 0, 0);

-- ==================== SCHEMA SUMMARY ====================
-- This clean schema includes only the essential tables:
-- 1. invoice_data_items - Core invoice data (Firebase replacement)
-- 2. users - User authentication and management  
-- 3. documents - File metadata storage (simplified)
-- 4. storage_usage - Storage quota tracking
--
-- Removed unused tables:
-- - document_categories, document_versions, document_shares
-- - document_comments, document_tags, document_texts
-- - user_sessions, activity_logs, notifications, trash
-- - All unused views
--
-- Benefits:
-- - Reduced complexity
-- - Better performance
-- - Easier maintenance
-- - Focused on actual application needs
