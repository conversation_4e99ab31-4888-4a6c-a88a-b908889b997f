<?php
/**
 * JWT Utility Class
 * Simple JWT implementation for authentication
 * 
 * @package MtcInvoice
 * @version 1.0
 */

class JWT {
    
    /**
     * Generate JWT token
     * 
     * @param array $payload
     * @param string $secret
     * @param int $expiration
     * @return string
     */
    public static function encode($payload, $secret, $expiration = 3600) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        
        $payload['iat'] = time();
        $payload['exp'] = time() + $expiration;
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * Decode and verify JWT token
     * 
     * @param string $jwt
     * @param string $secret
     * @return array|false
     */
    public static function decode($jwt, $secret) {
        $parts = explode('.', $jwt);
        
        if (count($parts) !== 3) {
            return false;
        }
        
        list($base64Header, $base64Payload, $base64Signature) = $parts;
        
        $header = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Header)), true);
        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Payload)), true);
        
        if (!$header || !$payload) {
            return false;
        }
        
        // Verify signature
        $signature = base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Signature));
        $expectedSignature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return false;
        }
        
        // Check expiration
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false;
        }
        
        return $payload;
    }
    
    /**
     * Get JWT token from Authorization header
     *
     * @return string|null
     */
    public static function getBearerToken() {
        // Try multiple methods to get the Authorization header
        $authHeader = null;

        // Method 1: getallheaders() function
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
            if (isset($headers['Authorization'])) {
                $authHeader = $headers['Authorization'];
            } elseif (isset($headers['authorization'])) {
                $authHeader = $headers['authorization'];
            }
        }

        // Method 2: $_SERVER variables
        if (!$authHeader) {
            if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
            } elseif (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
                $authHeader = $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
            }
        }

        // Method 3: Apache specific
        if (!$authHeader && function_exists('apache_request_headers')) {
            $headers = apache_request_headers();
            if (isset($headers['Authorization'])) {
                $authHeader = $headers['Authorization'];
            } elseif (isset($headers['authorization'])) {
                $authHeader = $headers['authorization'];
            }
        }

        // Extract Bearer token
        if ($authHeader) {
            $matches = [];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return trim($matches[1]);
            }
        }

        return null;
    }
    
    /**
     * Verify and get user from JWT token
     * 
     * @param string $secret
     * @return array|false
     */
    public static function verifyToken($secret) {
        $token = self::getBearerToken();
        
        if (!$token) {
            return false;
        }
        
        return self::decode($token, $secret);
    }
    
    /**
     * Middleware to check authentication
     *
     * @param string $secret
     * @return array User data if authenticated
     * @throws Exception if not authenticated
     */
    public static function requireAuth($secret) {
        // Debug logging
        error_log("=== JWT Authentication Debug ===");
        error_log("Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'unknown'));
        error_log("Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown'));

        // Check for token
        $token = self::getBearerToken();
        error_log("Token extracted: " . ($token ? "Yes (" . strlen($token) . " chars)" : "No"));

        if (!$token) {
            error_log("Authentication failed: No token provided");
            sendErrorResponse('Authentication required', 401);
        }

        // Verify token
        $user = self::decode($token, $secret);
        error_log("Token verification: " . ($user ? "Success" : "Failed"));

        if (!$user) {
            error_log("Authentication failed: Invalid token");
            sendErrorResponse('Authentication required', 401);
        }

        error_log("User authenticated: ID=" . $user['user_id'] . ", Email=" . $user['email']);
        return $user;
    }
    
    /**
     * Check if user has required role
     * 
     * @param array $user
     * @param string $required_role
     * @return bool
     */
    public static function hasRole($user, $required_role) {
        return isset($user['role']) && $user['role'] === $required_role;
    }
    
    /**
     * Require specific role
     * 
     * @param array $user
     * @param string $required_role
     * @throws Exception if user doesn't have required role
     */
    public static function requireRole($user, $required_role) {
        if (!self::hasRole($user, $required_role)) {
            sendErrorResponse('Insufficient permissions', 403);
        }
    }
}
