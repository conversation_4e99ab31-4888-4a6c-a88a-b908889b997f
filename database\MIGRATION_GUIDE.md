# Database Cleanup Migration Guide

## Overview
This guide provides step-by-step instructions for cleaning up the MtcInvoice database by removing unused tables and optimizing the structure.

## ⚠️ **IMPORTANT: Backup First!**
```bash
# Create a backup before running any migration
mysqldump -u root -p mtcinvoice_db > backup_before_cleanup_$(date +%Y%m%d_%H%M%S).sql
```

## Migration Files Created

1. **`cleanup_migration.sql`** - Main migration script
2. **`schema_clean.sql`** - New clean schema
3. **`cleanup_analysis_report.md`** - Detailed analysis
4. **`MIGRATION_GUIDE.md`** - This guide

## Step-by-Step Migration Process

### Step 1: Backup Current Database
```bash
# Navigate to your MySQL installation or use phpMyAdmin
mysqldump -u root -p mtcinvoice_db > mtcinvoice_backup_$(date +%Y%m%d).sql
```

### Step 2: Run the Cleanup Migration
```bash
# Option A: Using MySQL command line
mysql -u root -p mtcinvoice_db < database/cleanup_migration.sql

# Option B: Using phpMyAdmin
# - Open phpMyAdmin
# - Select mtcinvoice_db database
# - Go to SQL tab
# - Copy and paste contents of cleanup_migration.sql
# - Execute
```

### Step 3: Verify Migration Success
```sql
-- Check remaining tables
SHOW TABLES;

-- Should show only these 4 tables:
-- - documents
-- - invoice_data_items  
-- - storage_usage
-- - users

-- Check documents table structure
DESCRIBE documents;

-- Verify data integrity
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM invoice_data_items;
SELECT COUNT(*) FROM documents;
SELECT COUNT(*) FROM storage_usage;
```

### Step 4: Test Application Functionality

#### Test Admin Panel
1. Navigate to: `http://192.168.0.106/MtcInvoiceMasudvi/admin/login.php`
2. Login with: `<EMAIL>` / `admin123`
3. Test all sections:
   - Dashboard
   - Invoice management
   - File management
   - User management

#### Test API Endpoints
```bash
# Test authentication
curl -X POST http://192.168.0.106/MtcInvoiceMasudvi/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Test invoice data
curl -X GET http://192.168.0.106/MtcInvoiceMasudvi/api/invoice/

# Test file list
curl -X GET http://192.168.0.106/MtcInvoiceMasudvi/api/files/list \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Test Android App
1. Open MtcInvoice Android app
2. Test login functionality
3. Test invoice data loading
4. Test file upload/download

## What Was Removed

### Tables Removed (10+ tables)
- `document_categories`
- `document_versions`
- `document_shares`
- `document_comments`
- `document_tags`
- `document_texts`
- `user_sessions`
- `activity_logs`
- `notifications`
- `trash`

### Views Removed (3 views)
- `v_active_documents`
- `v_document_stats`
- `v_user_storage`

### Columns Removed from `documents` table
- `category_id`
- `is_encrypted`
- `encryption_key`
- `tags`
- `is_public`
- `expires_at`
- `deleted_at`

## What Was Kept

### Essential Tables (4 tables)
1. **`users`** - Authentication and user management
2. **`invoice_data_items`** - Core invoice data (Firebase replacement)
3. **`documents`** - File metadata (simplified)
4. **`storage_usage`** - Storage tracking

### Essential Indexes
- Primary keys on all tables
- Foreign key relationships
- Performance indexes for queries
- Full-text search on documents

## Rollback Plan

If you need to rollback:

```bash
# Restore from backup
mysql -u root -p -e "DROP DATABASE IF EXISTS mtcinvoice_db;"
mysql -u root -p -e "CREATE DATABASE mtcinvoice_db;"
mysql -u root -p mtcinvoice_db < your_backup_file.sql
```

## Performance Benefits

### Before Cleanup
- 15+ tables
- Complex relationships
- Unused indexes
- High maintenance overhead

### After Cleanup
- 4 essential tables
- Simple relationships
- Optimized indexes
- Low maintenance overhead

### Expected Improvements
- **Storage**: 60-70% reduction
- **Query Performance**: 15-20% improvement
- **Backup Time**: 50% faster
- **Maintenance**: Much simpler

## Troubleshooting

### Common Issues

1. **Foreign Key Constraints**
   ```sql
   -- If you get foreign key errors, disable checks temporarily
   SET FOREIGN_KEY_CHECKS = 0;
   -- Run your migration
   SET FOREIGN_KEY_CHECKS = 1;
   ```

2. **Permission Issues**
   ```sql
   -- Make sure user has DROP privileges
   GRANT ALL PRIVILEGES ON mtcinvoice_db.* TO 'your_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Application Errors After Migration**
   - Check error logs in `/api/logs/`
   - Verify all API endpoints are working
   - Clear any application caches

## Verification Checklist

- [ ] Database backup created
- [ ] Migration script executed successfully
- [ ] Only 4 tables remain
- [ ] Admin panel works
- [ ] API endpoints respond
- [ ] Android app connects
- [ ] File upload/download works
- [ ] User authentication works

## Support

If you encounter issues:
1. Check the backup was created successfully
2. Review error logs
3. Test each component individually
4. Rollback if necessary using the backup
