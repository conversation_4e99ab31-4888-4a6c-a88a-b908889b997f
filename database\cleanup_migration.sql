-- MtcInvoice Database Cleanup Migration
-- Version: 1.1
-- Description: Remove unused tables and optimize database structure
-- Date: 2025-06-15

-- WARNING: This script will permanently delete unused tables and their data
-- Make sure to backup your database before running this script

USE mtcinvoice_db;

-- ==================== BACKUP IMPORTANT DATA ====================
-- Before cleanup, ensure important data is preserved
-- The following tables will be KEPT as they are actively used:
-- - users (authentication and user management)
-- - invoice_data_items (core invoice data - replaces Firebase)
-- - documents (file storage metadata)
-- - storage_usage (storage tracking)

-- ==================== DROP UNUSED VIEWS ====================
-- Remove views that are not used in the application

DROP VIEW IF EXISTS `v_user_storage`;
DROP VIEW IF EXISTS `v_document_stats`;
DROP VIEW IF EXISTS `v_active_documents`;

-- ==================== DROP UNUSED TABLES ====================
-- Remove tables that have no references in PHP backend or Android app

-- Document management tables (not implemented in current version)
DROP TABLE IF EXISTS `document_comments`;
DROP TABLE IF EXISTS `document_shares`;
DROP TABLE IF EXISTS `document_tags`;
DROP TABLE IF EXISTS `document_texts`;
DROP TABLE IF EXISTS `document_versions`;
DROP TABLE IF EXISTS `document_categories`;

-- User session and activity tracking (not implemented)
DROP TABLE IF EXISTS `user_sessions`;
DROP TABLE IF EXISTS `activity_logs`;
DROP TABLE IF EXISTS `notifications`;

-- Trash/soft delete functionality (not implemented)
DROP TABLE IF EXISTS `trash`;

-- ==================== OPTIMIZE REMAINING TABLES ====================

-- Clean up documents table structure (remove unused columns)
-- Note: Only remove columns that are definitely not used
ALTER TABLE `documents` 
DROP COLUMN IF EXISTS `category_id`,
DROP COLUMN IF EXISTS `is_encrypted`,
DROP COLUMN IF EXISTS `encryption_key`,
DROP COLUMN IF EXISTS `tags`,
DROP COLUMN IF EXISTS `is_public`,
DROP COLUMN IF EXISTS `expires_at`,
DROP COLUMN IF EXISTS `deleted_at`;

-- Remove foreign key constraints that reference dropped tables
ALTER TABLE `documents` DROP FOREIGN KEY IF EXISTS `documents_ibfk_2`;

-- ==================== UPDATE INDEXES ====================
-- Remove indexes for dropped columns
DROP INDEX IF EXISTS `idx_documents_category` ON `documents`;
DROP INDEX IF EXISTS `idx_documents_deleted` ON `documents`;

-- Keep essential indexes
-- CREATE INDEX IF NOT EXISTS `idx_documents_user` ON `documents` (`user_id`);
-- CREATE INDEX IF NOT EXISTS `idx_documents_created` ON `documents` (`created_at`);

-- ==================== FINAL OPTIMIZATIONS ====================

-- Optimize table storage
OPTIMIZE TABLE `users`;
OPTIMIZE TABLE `invoice_data_items`;
OPTIMIZE TABLE `documents`;
OPTIMIZE TABLE `storage_usage`;

-- ==================== VERIFICATION QUERIES ====================
-- Run these queries after migration to verify the cleanup

-- Check remaining tables
-- SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH 
-- FROM information_schema.TABLES 
-- WHERE TABLE_SCHEMA = 'mtcinvoice_db' 
-- ORDER BY TABLE_NAME;

-- Check remaining columns in documents table
-- DESCRIBE documents;

-- ==================== MIGRATION COMPLETE ====================
-- The following tables remain active:
-- 1. users - User authentication and management
-- 2. invoice_data_items - Core invoice data (Firebase replacement)
-- 3. documents - File metadata (simplified structure)
-- 4. storage_usage - Storage quota tracking

SELECT 'Database cleanup migration completed successfully' AS status;
