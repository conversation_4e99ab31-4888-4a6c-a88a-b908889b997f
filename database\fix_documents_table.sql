-- Fix Documents Table Schema
-- Change ID from CHAR(36) UUID to INT AUTO_INCREMENT
-- This migration will preserve existing data while optimizing the table structure

-- Step 1: Create backup table
CREATE TABLE `documents_backup` AS SELECT * FROM `documents`;

-- Step 2: Create new documents table with proper structure
DROP TABLE IF EXISTS `documents_new`;
CREATE TABLE `documents_new` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL, -- Keep UUID for external references
  `user_id` INT(11) NOT NULL,
  `original_name` VARCHAR(255) NOT NULL,
  `storage_path` VARCHAR(512) NOT NULL,
  `file_name` VARCHAR(255) NOT NULL,
  `file_size` BIGINT NOT NULL DEFAULT 0, -- in bytes
  `file_type` VARCHAR(100) NOT NULL,
  `file_hash` VARCHAR(64) NOT NULL, -- SHA-256 hash for deduplication
  `description` TEXT,
  `download_count` INT DEFAULT 0,
  `last_downloaded_at` DATETIME DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uuid` (`uuid`),
  UNIQUE KEY `idx_file_hash_user` (`file_hash`, `user_id`), -- Prevent duplicate uploads
  KEY `idx_user` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  FULLTEXT KEY `ft_search` (`original_name`, `description`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Step 3: Migrate existing data
INSERT INTO `documents_new` (
  `uuid`, 
  `user_id`, 
  `original_name`, 
  `storage_path`, 
  `file_name`, 
  `file_size`, 
  `file_type`, 
  `file_hash`, 
  `description`, 
  `download_count`, 
  `last_downloaded_at`, 
  `created_at`, 
  `updated_at`
)
SELECT 
  `id` as `uuid`, -- Old ID becomes UUID
  `user_id`, 
  `original_name`, 
  `storage_path`, 
  `file_name`, 
  COALESCE(`file_size`, 0) as `file_size`, -- Handle NULL file_size
  `file_type`, 
  `file_hash`, 
  `description`, 
  COALESCE(`download_count`, 0) as `download_count`,
  `last_downloaded_at`, 
  `created_at`, 
  `updated_at`
FROM `documents`;

-- Step 4: Replace old table with new one
DROP TABLE `documents`;
RENAME TABLE `documents_new` TO `documents`;

-- Step 5: Update any related tables that reference documents
-- Note: This assumes no foreign key constraints to documents.id
-- If there are, they would need to be updated as well

-- Step 6: Verify the migration
SELECT 
  COUNT(*) as total_documents,
  MIN(id) as min_id,
  MAX(id) as max_id,
  COUNT(DISTINCT uuid) as unique_uuids
FROM `documents`;

-- Step 7: Show new table structure
DESCRIBE `documents`;
