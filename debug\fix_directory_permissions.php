<?php
/**
 * Fix Directory Permissions Script
 * Ensure uploads directory has proper permissions for file storage
 */

require_once '../api/config/config.php';

echo "<h1>Directory Permissions Fix</h1>\n";
echo "<pre>\n";

echo "=== CURRENT SYSTEM INFO ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Operating System: " . php_uname() . "\n";
echo "Web Server User: " . get_current_user() . "\n";
echo "Process Owner: " . (function_exists('posix_getpwuid') ? posix_getpwuid(posix_geteuid())['name'] : 'Unknown') . "\n";
echo "Current Working Directory: " . getcwd() . "\n\n";

echo "=== UPLOAD DIRECTORY ANALYSIS ===\n";

$directories = [
    'uploads' => '../uploads/',
    'uploads_pdfs' => '../uploads/pdfs/',
    'config_upload_dir' => UPLOAD_DIR,
    'config_upload_pdfs' => UPLOAD_DIR . 'pdfs/'
];

foreach ($directories as $name => $dir) {
    echo "--- $name: $dir ---\n";
    
    if (is_dir($dir)) {
        echo "✅ Directory exists\n";
        echo "Real path: " . realpath($dir) . "\n";
        
        // Get current permissions
        $perms = fileperms($dir);
        $perms_octal = substr(sprintf('%o', $perms), -4);
        echo "Current permissions: $perms_octal\n";
        
        // Check ownership
        if (function_exists('fileowner') && function_exists('posix_getpwuid')) {
            $owner_uid = fileowner($dir);
            $owner_info = posix_getpwuid($owner_uid);
            echo "Owner: " . $owner_info['name'] . " (UID: $owner_uid)\n";
        }
        
        if (function_exists('filegroup') && function_exists('posix_getgrgid')) {
            $group_gid = filegroup($dir);
            $group_info = posix_getgrgid($group_gid);
            echo "Group: " . $group_info['name'] . " (GID: $group_gid)\n";
        }
        
        // Check access
        echo "Readable: " . (is_readable($dir) ? 'YES' : 'NO') . "\n";
        echo "Writable: " . (is_writable($dir) ? 'YES' : 'NO') . "\n";
        echo "Executable: " . (is_executable($dir) ? 'YES' : 'NO') . "\n";
        
        // Test write access
        $test_file = $dir . 'test_write_' . time() . '.tmp';
        echo "Testing write access...\n";
        
        if (file_put_contents($test_file, 'test')) {
            echo "✅ Write test successful\n";
            unlink($test_file);
        } else {
            echo "❌ Write test failed\n";
            echo "Error: " . (error_get_last()['message'] ?? 'Unknown error') . "\n";
            
            // Try to fix permissions
            echo "Attempting to fix permissions...\n";
            
            if (chmod($dir, 0777)) {
                echo "✅ Permissions changed to 777\n";
                
                // Test write again
                if (file_put_contents($test_file, 'test')) {
                    echo "✅ Write test now successful\n";
                    unlink($test_file);
                } else {
                    echo "❌ Write test still fails\n";
                }
            } else {
                echo "❌ Failed to change permissions\n";
            }
        }
        
    } else {
        echo "❌ Directory does not exist\n";
        echo "Attempting to create directory...\n";
        
        if (mkdir($dir, 0777, true)) {
            echo "✅ Directory created successfully\n";
            echo "New permissions: " . substr(sprintf('%o', fileperms($dir)), -4) . "\n";
            
            // Test write access
            $test_file = $dir . 'test_write_' . time() . '.tmp';
            if (file_put_contents($test_file, 'test')) {
                echo "✅ Write test successful\n";
                unlink($test_file);
            } else {
                echo "❌ Write test failed even after creation\n";
            }
        } else {
            echo "❌ Failed to create directory\n";
            echo "Error: " . (error_get_last()['message'] ?? 'Unknown error') . "\n";
        }
    }
    echo "\n";
}

echo "=== RECOMMENDED ACTIONS ===\n";

// Check if we're on Windows or Unix-like system
$is_windows = (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN');

if ($is_windows) {
    echo "Windows system detected:\n";
    echo "1. Ensure the web server (Apache/IIS) has write permissions to the uploads folder\n";
    echo "2. Check Windows folder permissions in File Explorer\n";
    echo "3. Right-click uploads folder → Properties → Security → Edit\n";
    echo "4. Give 'Full Control' to 'IIS_IUSRS' or 'Everyone' (for testing)\n";
} else {
    echo "Unix-like system detected:\n";
    echo "1. Set directory permissions to 755 or 777:\n";
    echo "   chmod 755 uploads/\n";
    echo "   chmod 755 uploads/pdfs/\n";
    echo "2. Ensure web server user (www-data, apache, nginx) owns the directory:\n";
    echo "   chown -R www-data:www-data uploads/\n";
    echo "3. If still failing, try 777 permissions (less secure):\n";
    echo "   chmod 777 uploads/\n";
    echo "   chmod 777 uploads/pdfs/\n";
}

echo "\n=== PHP CONFIGURATION CHECK ===\n";
echo "file_uploads: " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "upload_tmp_dir: " . (ini_get('upload_tmp_dir') ?: 'Default system temp') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . " seconds\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";

$temp_dir = sys_get_temp_dir();
echo "System temp directory: $temp_dir\n";
echo "Temp dir writable: " . (is_writable($temp_dir) ? 'YES' : 'NO') . "\n";

echo "\n=== APACHE/WEB SERVER CHECK ===\n";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    echo "Apache modules loaded: " . count($modules) . "\n";
    $relevant_modules = ['mod_rewrite', 'mod_php', 'mod_ssl'];
    foreach ($relevant_modules as $module) {
        echo "$module: " . (in_array($module, $modules) ? 'Loaded' : 'Not loaded') . "\n";
    }
} else {
    echo "Apache module information not available\n";
}

echo "\n=== NEXT STEPS ===\n";
echo "1. Run the diagnostic upload test to see detailed error messages\n";
echo "2. Check PHP error logs for specific upload failures\n";
echo "3. If permissions are correct, check move_uploaded_file() specific issues\n";
echo "4. Verify the upload.php script is receiving files correctly\n";

echo "</pre>";
?>
