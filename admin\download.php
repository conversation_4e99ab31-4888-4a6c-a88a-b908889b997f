<?php
/**
 * Admin File Download Handler
 * Handles secure file downloads from the admin panel
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require admin login
requireLogin();

// Get file parameter
$file_id = $_GET['file'] ?? '';
$action = $_GET['action'] ?? 'download'; // 'download' or 'view'

if (empty($file_id)) {
    http_response_code(400);
    die('File ID required');
}

try {
    $pdo = getDbConnection();
    
    // Get file information from database
    $stmt = $pdo->prepare("
        SELECT 
            d.id,
            d.original_name,
            d.file_name,
            d.storage_path,
            d.file_size,
            d.file_type,
            d.created_at,
            u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.user_id = u.id
        WHERE d.id = ?
    ");
    $stmt->execute([$file_id]);
    $file_info = $stmt->fetch();
    
    if (!$file_info) {
        http_response_code(404);
        die('File not found in database');
    }
    
    // Check if physical file exists
    $file_paths = [
        '../uploads/' . $file_info['storage_path'],
        '../uploads/pdfs/' . $file_info['file_name'],
        '../uploads/pdfs/' . $file_info['storage_path']
    ];
    
    $file_path = null;
    foreach ($file_paths as $path) {
        if (file_exists($path)) {
            $file_path = $path;
            break;
        }
    }
    
    if (!$file_path) {
        http_response_code(404);
        error_log("Physical file not found for ID: $file_id. Checked paths: " . implode(', ', $file_paths));
        die('Physical file not found. File may have been moved or deleted.');
    }
    
    // Verify file size matches database
    $actual_size = filesize($file_path);
    if ($actual_size != $file_info['file_size']) {
        error_log("File size mismatch for ID: $file_id. DB: {$file_info['file_size']}, Actual: $actual_size");
    }
    
    // Update download count
    $stmt = $pdo->prepare("UPDATE documents SET download_count = download_count + 1 WHERE id = ?");
    $stmt->execute([$file_id]);
    
    // Log download activity
    $current_user = getCurrentUser();
    error_log("File downloaded by admin: " . $current_user['email'] . " - " . $file_info['original_name']);
    
    // Determine content type
    $extension = strtolower(pathinfo($file_info['original_name'], PATHINFO_EXTENSION));
    $content_type = match($extension) {
        'pdf' => 'application/pdf',
        'jpg', 'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        default => 'application/octet-stream'
    };
    
    // Set headers for file download/view
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . $actual_size);
    header('Cache-Control: private, must-revalidate');
    header('Pragma: private');
    header('Expires: 0');
    
    if ($action === 'download') {
        // Force download
        header('Content-Disposition: attachment; filename="' . addslashes($file_info['original_name']) . '"');
    } else {
        // View in browser (for PDFs and images)
        if (in_array($extension, ['pdf', 'jpg', 'jpeg', 'png'])) {
            header('Content-Disposition: inline; filename="' . addslashes($file_info['original_name']) . '"');
        } else {
            // Force download for other file types
            header('Content-Disposition: attachment; filename="' . addslashes($file_info['original_name']) . '"');
        }
    }
    
    // Output file content
    $handle = fopen($file_path, 'rb');
    if ($handle) {
        while (!feof($handle)) {
            echo fread($handle, 8192);
            flush();
        }
        fclose($handle);
    } else {
        http_response_code(500);
        die('Failed to read file');
    }
    
} catch (Exception $e) {
    error_log("File download error: " . $e->getMessage());
    http_response_code(500);
    die('Download failed: ' . $e->getMessage());
}
?>
