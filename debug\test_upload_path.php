<?php
/**
 * Test Upload Path Resolution
 * Check if the upload directory path is correctly resolved
 */

require_once '../api/config/config.php';

echo "<h1>Upload Path Test</h1>\n";
echo "<pre>\n";

echo "=== PATH RESOLUTION TEST ===\n";
echo "Current working directory: " . getcwd() . "\n";
echo "UPLOAD_DIR constant: " . UPLOAD_DIR . "\n";

$upload_dir = UPLOAD_DIR . 'pdfs/';
echo "Full upload directory path: " . $upload_dir . "\n";
echo "Absolute path: " . realpath($upload_dir) . "\n";

echo "\n=== DIRECTORY CHECKS ===\n";
echo "Directory exists: " . (is_dir($upload_dir) ? 'YES' : 'NO') . "\n";
echo "Directory is writable: " . (is_writable($upload_dir) ? 'YES' : 'NO') . "\n";

if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    echo "Files in directory: " . count($files) . "\n";
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "  - $file\n";
        }
    }
}

echo "\n=== CONSTANTS CHECK ===\n";
echo "MAX_FILE_SIZE: " . MAX_FILE_SIZE . " bytes (" . (MAX_FILE_SIZE / 1024 / 1024) . " MB)\n";
echo "ALLOWED_FILE_TYPES: " . implode(', ', ALLOWED_FILE_TYPES) . "\n";
echo "BASE_URL: " . BASE_URL . "\n";

echo "\n=== DATABASE CONNECTION TEST ===\n";
try {
    require_once '../api/config/database.php';
    $database = new Database();
    $pdo = $database->getConnection();
    
    if ($pdo) {
        echo "✅ Database connection successful\n";
        
        // Check if documents table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'documents'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Documents table exists\n";
            
            // Check table structure
            $stmt = $pdo->query("DESCRIBE documents");
            $columns = $stmt->fetchAll();
            echo "Documents table columns:\n";
            foreach ($columns as $column) {
                echo "  - {$column['Field']} ({$column['Type']})\n";
            }
        } else {
            echo "❌ Documents table does not exist\n";
        }
    } else {
        echo "❌ Database connection failed\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n=== FUNCTION AVAILABILITY TEST ===\n";
echo "generatePushKey function: " . (function_exists('generatePushKey') ? 'EXISTS' : 'MISSING') . "\n";
echo "sendErrorResponse function: " . (function_exists('sendErrorResponse') ? 'EXISTS' : 'MISSING') . "\n";
echo "sendSuccessResponse function: " . (function_exists('sendSuccessResponse') ? 'EXISTS' : 'MISSING') . "\n";
echo "logActivity function: " . (function_exists('logActivity') ? 'EXISTS' : 'MISSING') . "\n";

if (function_exists('generatePushKey')) {
    echo "Sample push key: " . generatePushKey() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
echo "</pre>";
?>
