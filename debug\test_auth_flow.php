<?php
/**
 * Authentication Flow Debug Script
 * Tests JWT authentication flow to diagnose 401 errors
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once 'api/config/config.php';
require_once 'api/config/database.php';
require_once 'api/utils/jwt.php';

// Helper functions
function sendErrorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message]);
    exit;
}

function sendSuccessResponse($data, $message = 'Success') {
    echo json_encode(['success' => true, 'message' => $message, 'data' => $data]);
}

echo "=== JWT AUTHENTICATION FLOW DEBUG ===\n\n";

// Test 1: Database Connection
echo "1. Testing Database Connection...\n";
$database = new Database();
$pdo = $database->getConnection();

if (!$pdo) {
    echo "❌ Database connection failed\n";
    exit(1);
} else {
    echo "✅ Database connection successful\n\n";
}

// Test 2: User Authentication
echo "2. Testing User Authentication...\n";
$email = '<EMAIL>';
$password = 'admin123';

try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 1");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        echo "✅ User authentication successful\n";
        echo "   - User ID: {$user['id']}\n";
        echo "   - Name: {$user['name']}\n";
        echo "   - Role: {$user['role']}\n\n";
    } else {
        echo "❌ User authentication failed\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ Authentication error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 3: JWT Token Generation
echo "3. Testing JWT Token Generation...\n";
$payload = [
    'user_id' => $user['id'],
    'email' => $user['email'],
    'name' => $user['name'],
    'role' => $user['role']
];

try {
    $token = JWT::encode($payload, JWT_SECRET_KEY, JWT_EXPIRATION_TIME);
    echo "✅ JWT token generated successfully\n";
    echo "   - Token length: " . strlen($token) . " characters\n";
    echo "   - Token preview: " . substr($token, 0, 50) . "...\n";
    echo "   - Secret key: " . JWT_SECRET_KEY . "\n";
    echo "   - Expiration: " . JWT_EXPIRATION_TIME . " seconds\n\n";
} catch (Exception $e) {
    echo "❌ JWT token generation failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 4: JWT Token Verification
echo "4. Testing JWT Token Verification...\n";
try {
    $decoded = JWT::decode($token, JWT_SECRET_KEY);
    if ($decoded) {
        echo "✅ JWT token verification successful\n";
        echo "   - User ID: {$decoded['user_id']}\n";
        echo "   - Email: {$decoded['email']}\n";
        echo "   - Issued at: " . date('Y-m-d H:i:s', $decoded['iat']) . "\n";
        echo "   - Expires at: " . date('Y-m-d H:i:s', $decoded['exp']) . "\n\n";
    } else {
        echo "❌ JWT token verification failed\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ JWT verification error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 5: Simulate Authorization Header
echo "5. Testing Authorization Header Parsing...\n";
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $token;

try {
    $extractedToken = JWT::getBearerToken();
    if ($extractedToken === $token) {
        echo "✅ Authorization header parsing successful\n";
        echo "   - Extracted token matches original\n\n";
    } else {
        echo "❌ Authorization header parsing failed\n";
        echo "   - Expected: " . substr($token, 0, 30) . "...\n";
        echo "   - Got: " . substr($extractedToken, 0, 30) . "...\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ Authorization header error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 6: Test File List API Endpoint
echo "6. Testing File List API Endpoint...\n";
$api_url = 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/list';

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json'
    ],
    CURLOPT_TIMEOUT => 10,
    CURLOPT_VERBOSE => true
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "   - HTTP Status Code: $http_code\n";
if ($curl_error) {
    echo "   - cURL Error: $curl_error\n";
}

if ($http_code === 200) {
    echo "✅ File list API call successful\n";
    echo "   - Response: " . substr($response, 0, 200) . "...\n\n";
} else {
    echo "❌ File list API call failed\n";
    echo "   - Response: $response\n\n";
}

// Test 7: Check Documents Table Structure
echo "7. Checking Documents Table Structure...\n";
try {
    $stmt = $pdo->query("DESCRIBE documents");
    $columns = $stmt->fetchAll();
    
    echo "✅ Documents table structure:\n";
    foreach ($columns as $column) {
        echo "   - {$column['Field']}: {$column['Type']} ({$column['Key']})\n";
    }
    echo "\n";
    
    // Check if ID field is using UUID
    $id_column = array_filter($columns, function($col) { return $col['Field'] === 'id'; });
    $id_column = reset($id_column);
    
    if ($id_column && strpos($id_column['Type'], 'char(36)') !== false) {
        echo "⚠️  Documents table ID field is using CHAR(36) UUID instead of AUTO_INCREMENT\n";
        echo "   - Current type: {$id_column['Type']}\n";
        echo "   - Recommendation: Change to INT AUTO_INCREMENT for better performance\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Documents table check failed: " . $e->getMessage() . "\n";
}

// Test 8: Check Sample Documents
echo "8. Checking Sample Documents...\n";
try {
    $stmt = $pdo->query("SELECT id, original_name, file_size, created_at FROM documents LIMIT 5");
    $documents = $stmt->fetchAll();
    
    if (count($documents) > 0) {
        echo "✅ Found " . count($documents) . " documents:\n";
        foreach ($documents as $doc) {
            echo "   - ID: {$doc['id']} | Name: {$doc['original_name']} | Size: {$doc['file_size']} bytes\n";
        }
    } else {
        echo "ℹ️  No documents found in database\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Documents query failed: " . $e->getMessage() . "\n";
}

echo "=== DEBUG COMPLETE ===\n";
echo "Summary:\n";
echo "- Database: Connected\n";
echo "- Authentication: Working\n";
echo "- JWT Generation: Working\n";
echo "- JWT Verification: Working\n";
echo "- Authorization Header: Working\n";
echo "- API Endpoint: " . ($http_code === 200 ? "Working" : "Failed ($http_code)") . "\n";
echo "- Documents Table: UUID-based ID (needs optimization)\n";
