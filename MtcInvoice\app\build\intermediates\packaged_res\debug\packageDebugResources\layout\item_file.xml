<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:background="@drawable/rounded">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- File Icon -->
        <ImageView
            android:id="@+id/fileIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/baseline_wallet_24"
            android:contentDescription="File icon" />

        <!-- File Information -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/fileName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="File Name"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:maxLines="2"
                android:ellipsize="end" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/fileSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 KB"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" • "
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray" />

                <TextView
                    android:id="@+id/uploadDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Upload Date"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray" />

            </LinearLayout>

        </LinearLayout>

        <!-- Delete Button -->
        <ImageView
            android:id="@+id/deleteButton"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/delete"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:contentDescription="Delete file" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
