<?php
/**
 * File List API
 * List uploaded files (replaces Firebase Storage list functionality)
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../utils/jwt.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Authentication required
    $user = JWT::requireAuth(JWT_SECRET_KEY);

    // Get database connection
    $database = new Database();
    $pdo = $database->getConnection();

    if (!$pdo) {
        sendErrorResponse('Database connection failed', 500);
    }

    // Apply pagination parameters
    $limit = isset($_GET['limit']) ? min((int)$_GET['limit'], 100) : 50;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;

    // Get total count for pagination
    $count_stmt = $pdo->prepare("SELECT COUNT(*) FROM documents WHERE user_id = ? AND deleted_at IS NULL");
    $count_stmt->execute([$user['user_id']]);
    $total_files = $count_stmt->fetchColumn();

    // Get files from database for the current user
    $stmt = $pdo->prepare("
        SELECT
            id,
            original_name,
            file_name,
            storage_path,
            file_size,
            file_type,
            file_hash,
            created_at,
            download_count
        FROM documents
        WHERE user_id = ? AND deleted_at IS NULL
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    ");

    $stmt->execute([$user['user_id'], $limit, $offset]);
    $db_files = $stmt->fetchAll();

    // Format files for API response
    $files = [];
    foreach ($db_files as $file) {
        $file_info = [
            'id' => $file['id'],
            'name' => $file['file_name'],
            'original_name' => $file['original_name'],
            'size' => (int)$file['file_size'],
            'type' => $file['file_type'],
            'extension' => strtolower(pathinfo($file['original_name'], PATHINFO_EXTENSION)),
            'hash' => $file['file_hash'],
            'created_at' => $file['created_at'],
            'download_count' => (int)$file['download_count'],
            'download_url' => BASE_URL . 'files/download.php?file=' . urlencode($file['file_name'])
        ];

        // Check if physical file exists
        $file_path = UPLOAD_DIR . $file['storage_path'];
        $file_info['exists'] = file_exists($file_path);

        $files[] = $file_info;
    }

    sendSuccessResponse([
        'files' => $files,
        'pagination' => [
            'total' => $total_files,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + count($files)) < $total_files
        ]
    ], 'Files retrieved successfully');
    
} catch (Exception $e) {
    error_log("File list error: " . $e->getMessage());
    sendErrorResponse('Failed to retrieve file list', 500);
}
