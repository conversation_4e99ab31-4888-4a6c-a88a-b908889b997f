<?php
/**
 * Test File Upload API
 * Test the file upload endpoint to ensure it works correctly
 */

echo "<h1>File Upload API Test</h1>\n";
echo "<pre>\n";

// Test 1: Check if upload endpoint exists
echo "=== TEST 1: CHECK UPLOAD ENDPOINT ===\n";
$upload_url = 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/upload.php';

// Test with GET request (should fail with method not allowed)
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $upload_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "GET Request to upload endpoint:\n";
echo "HTTP Code: $http_code\n";

if ($http_code == 405) {
    echo "✅ Endpoint correctly rejects GET requests\n";
} else {
    echo "❌ Unexpected response to GET request\n";
}

// Test 2: Check authentication requirement
echo "\n=== TEST 2: CHECK AUTHENTICATION REQUIREMENT ===\n";

// Create a test file
$test_file_content = "This is a test PDF content for upload testing.";
$test_file_path = sys_get_temp_dir() . '/test_upload.pdf';
file_put_contents($test_file_path, $test_file_content);

// Test POST without authentication
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $upload_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, [
    'file' => new CURLFile($test_file_path, 'application/pdf', 'test.pdf')
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "POST Request without authentication:\n";
echo "HTTP Code: $http_code\n";

// Extract response body
$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$response_body = substr($response, $header_size);

echo "Response body preview: " . substr($response_body, 0, 200) . "...\n";

if ($http_code == 401) {
    echo "✅ Endpoint correctly requires authentication\n";
} else {
    echo "❌ Authentication requirement issue\n";
}

// Test 3: Check if response is JSON
echo "\n=== TEST 3: CHECK JSON RESPONSE FORMAT ===\n";

$json_data = json_decode($response_body, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Response is valid JSON\n";
    echo "Response structure:\n";
    print_r($json_data);
} else {
    echo "❌ Response is not valid JSON\n";
    echo "JSON Error: " . json_last_error_msg() . "\n";
    echo "Raw response: " . $response_body . "\n";
}

// Test 4: Test with valid authentication
echo "\n=== TEST 4: TEST WITH AUTHENTICATION ===\n";

// First, let's try to login to get a token
$login_url = 'http://192.168.0.106/MtcInvoiceMasudvi/api/auth/login.php';
$login_data = json_encode([
    'email' => '<EMAIL>',
    'password' => 'admin123'
]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $login_data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($login_data)
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$login_response = curl_exec($ch);
$login_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Login attempt:\n";
echo "HTTP Code: $login_http_code\n";

$login_data = json_decode($login_response, true);
if ($login_http_code == 200 && isset($login_data['data']['token'])) {
    echo "✅ Login successful\n";
    $token = $login_data['data']['token'];
    
    // Now test file upload with authentication
    echo "\nTesting file upload with authentication:\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $upload_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, [
        'file' => new CURLFile($test_file_path, 'application/pdf', 'test.pdf')
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $upload_response = curl_exec($ch);
    $upload_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Upload HTTP Code: $upload_http_code\n";
    echo "Upload Response: " . substr($upload_response, 0, 500) . "...\n";
    
    $upload_data = json_decode($upload_response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Upload response is valid JSON\n";
        if ($upload_http_code == 200 && isset($upload_data['success']) && $upload_data['success']) {
            echo "✅ File upload successful!\n";
        } else {
            echo "❌ File upload failed\n";
            print_r($upload_data);
        }
    } else {
        echo "❌ Upload response is not valid JSON\n";
        echo "Raw response: " . $upload_response . "\n";
    }
    
} else {
    echo "❌ Login failed\n";
    echo "Response: " . $login_response . "\n";
}

// Cleanup
unlink($test_file_path);

echo "\n=== TEST COMPLETE ===\n";
echo "</pre>";
?>
