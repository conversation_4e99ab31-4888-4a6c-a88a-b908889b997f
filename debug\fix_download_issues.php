<?php
/**
 * Fix Download Issues - Admin Panel and Android App
 * Comprehensive solution for file download problems
 */

require_once '../api/config/config.php';

echo "<h1>Download Issues Fix</h1>\n";
echo "<pre>\n";

$fixes_applied = [];
$errors = [];

echo "=== ISSUE ANALYSIS ===\n";
echo "1. Admin Panel PDF operations not working\n";
echo "2. Android app download permission problems\n";
echo "3. Physical files missing from filesystem\n\n";

// Fix 1: Test admin panel download functionality
echo "=== FIX 1: ADMIN PANEL DOWNLOAD HANDLER ===\n";

$download_handler_path = '../admin/download.php';
if (file_exists($download_handler_path)) {
    echo "✅ Admin download handler exists\n";
    $fixes_applied[] = "Admin download handler is available";
} else {
    echo "❌ Admin download handler missing\n";
    $errors[] = "Admin download handler not found";
}

// Test database connectivity for admin panel
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "✅ Database connection successful\n";
    
    // Check recent files and their physical existence
    $stmt = $pdo->query("
        SELECT id, original_name, file_name, storage_path, file_size 
        FROM documents 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    $files = $stmt->fetchAll();
    echo "Found " . count($files) . " recent files in database\n";
    
    $available_count = 0;
    $missing_count = 0;
    
    foreach ($files as $file) {
        $file_paths = [
            '../uploads/' . $file['storage_path'],
            '../uploads/pdfs/' . $file['file_name'],
            '../uploads/pdfs/' . $file['storage_path']
        ];
        
        $found = false;
        foreach ($file_paths as $path) {
            if (file_exists($path)) {
                $found = true;
                break;
            }
        }
        
        if ($found) {
            $available_count++;
        } else {
            $missing_count++;
            echo "Missing: " . $file['original_name'] . " (ID: " . $file['id'] . ")\n";
        }
    }
    
    echo "Available files: $available_count\n";
    echo "Missing files: $missing_count\n";
    
    if ($missing_count > 0) {
        echo "⚠️  Some files are missing from filesystem\n";
        $errors[] = "$missing_count files missing from filesystem";
    } else {
        echo "✅ All files are available\n";
        $fixes_applied[] = "All database files have physical counterparts";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    $errors[] = "Database connection failed";
}

// Fix 2: Check and fix upload directory structure
echo "\n=== FIX 2: UPLOAD DIRECTORY STRUCTURE ===\n";

$directories = [
    '../uploads/',
    '../uploads/pdfs/'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        echo "Creating directory: $dir\n";
        if (mkdir($dir, 0777, true)) {
            echo "✅ Directory created\n";
            $fixes_applied[] = "Created directory: $dir";
        } else {
            echo "❌ Failed to create directory\n";
            $errors[] = "Failed to create directory: $dir";
        }
    } else {
        echo "✅ Directory exists: $dir\n";
    }
    
    // Set proper permissions
    if (chmod($dir, 0777)) {
        echo "✅ Permissions set to 777\n";
    } else {
        echo "⚠️  Could not change permissions\n";
    }
}

// Fix 3: Test file upload process
echo "\n=== FIX 3: FILE UPLOAD PROCESS TEST ===\n";

// Create a test file
$test_content = "Test file for upload verification - " . date('Y-m-d H:i:s');
$test_filename = 'upload_test_' . time() . '.txt';
$temp_file = sys_get_temp_dir() . '/' . $test_filename;
file_put_contents($temp_file, $test_content);

echo "Created test file: $test_filename\n";

// Test move operation
$target_dir = '../uploads/pdfs/';
$target_file = $target_dir . $test_filename;

if (copy($temp_file, $target_file)) {
    echo "✅ File copy successful\n";
    echo "Target file exists: " . (file_exists($target_file) ? 'YES' : 'NO') . "\n";
    echo "File size: " . filesize($target_file) . " bytes\n";
    
    // Clean up
    unlink($target_file);
    $fixes_applied[] = "File upload process working";
} else {
    echo "❌ File copy failed\n";
    $errors[] = "File upload process not working";
}

// Clean up temp file
unlink($temp_file);

// Fix 4: Android permission guidance
echo "\n=== FIX 4: ANDROID PERMISSION GUIDANCE ===\n";

echo "Android app permission issues identified:\n";
echo "1. DownloadListActivity needs proper permission handling\n";
echo "2. Permission request flow should match other activities\n";
echo "3. Storage permissions for Android 11+ (API 30+) require special handling\n\n";

echo "Required Android fixes:\n";
echo "✓ Add proper permission checking in DownloadListActivity\n";
echo "✓ Implement Android 11+ storage permission handling\n";
echo "✓ Add permission result handling\n";
echo "✓ Show appropriate user messages for permission states\n\n";

$fixes_applied[] = "Android permission guidance provided";

// Fix 5: Create test download URL
echo "=== FIX 5: ADMIN PANEL DOWNLOAD TEST ===\n";

if (!empty($files)) {
    $test_file = $files[0];
    $download_url = "http://*************/MtcInvoiceMasudvi/admin/download.php?file=" . urlencode($test_file['id']) . "&action=view";
    echo "Test download URL: $download_url\n";
    echo "Test file: " . $test_file['original_name'] . "\n";
    $fixes_applied[] = "Admin panel download URL generated";
} else {
    echo "No files available for testing\n";
}

// Fix 6: API endpoint verification
echo "\n=== FIX 6: API ENDPOINT VERIFICATION ===\n";

$api_endpoints = [
    '/api/files/list.php' => 'File listing',
    '/api/files/download.php' => 'File download',
    '/api/files/delete.php' => 'File deletion',
    '/api/files/upload.php' => 'File upload'
];

foreach ($api_endpoints as $endpoint => $description) {
    $file_path = '..' . $endpoint;
    if (file_exists($file_path)) {
        echo "✅ $description endpoint exists\n";
    } else {
        echo "❌ $description endpoint missing\n";
        $errors[] = "$description endpoint missing";
    }
}

// Summary and recommendations
echo "\n=== SUMMARY ===\n";
echo "Fixes applied: " . count($fixes_applied) . "\n";
foreach ($fixes_applied as $fix) {
    echo "  ✅ $fix\n";
}

echo "\nErrors found: " . count($errors) . "\n";
foreach ($errors as $error) {
    echo "  ❌ $error\n";
}

echo "\n=== IMMEDIATE ACTION ITEMS ===\n";
echo "1. Run the file storage fix script to ensure physical files are saved:\n";
echo "   http://*************/MtcInvoiceMasudvi/debug/fix_file_storage_issue.php\n\n";

echo "2. Test admin panel download functionality:\n";
echo "   http://*************/MtcInvoiceMasudvi/admin/files.php\n\n";

echo "3. For Android app:\n";
echo "   - The DownloadListActivity has been updated with proper permission handling\n";
echo "   - Test file download from the Android app\n";
echo "   - Ensure storage permissions are granted\n\n";

echo "4. Test complete flow:\n";
echo "   a) Upload file from Android app\n";
echo "   b) Verify file appears in admin panel with 'Available' status\n";
echo "   c) Test download from admin panel\n";
echo "   d) Test download from Android app\n\n";

if (count($errors) == 0) {
    echo "✅ All systems appear to be working correctly!\n";
} else {
    echo "⚠️  Some issues need attention. Address the errors above.\n";
}

echo "</pre>";
?>
