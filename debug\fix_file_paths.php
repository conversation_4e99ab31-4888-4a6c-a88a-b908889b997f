<?php
/**
 * Fix File Path Issues
 * Update database records and verify file locations
 */

require_once '../api/config/config.php';

echo "<h1>File Path Fix Script</h1>\n";
echo "<pre>\n";

try {
    // Connect to database
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "=== ANALYZING CURRENT FILE RECORDS ===\n";
    
    // Get all files
    $stmt = $pdo->query("SELECT * FROM documents ORDER BY created_at DESC");
    $files = $stmt->fetchAll();
    
    echo "Found " . count($files) . " files in database\n\n";
    
    $fixed_count = 0;
    $already_correct = 0;
    $missing_files = 0;
    
    foreach ($files as $file) {
        echo "--- Processing File: " . $file['original_name'] . " ---\n";
        echo "ID: " . $file['id'] . "\n";
        echo "Current storage_path: " . $file['storage_path'] . "\n";
        echo "File name: " . $file['file_name'] . "\n";
        
        // Check where admin panel expects the file
        $admin_expected_path = '../uploads/' . $file['storage_path'];
        echo "Admin expects file at: $admin_expected_path\n";
        echo "File exists there: " . (file_exists($admin_expected_path) ? 'YES' : 'NO') . "\n";
        
        if (file_exists($admin_expected_path)) {
            echo "✅ File is correctly located\n";
            $already_correct++;
        } else {
            echo "❌ File not found at expected location\n";
            
            // Search for the file in possible locations
            $search_locations = [
                '../uploads/pdfs/' . $file['file_name'],
                '../uploads/' . $file['file_name'],
                'uploads/pdfs/' . $file['file_name'],
                'uploads/' . $file['file_name'],
            ];
            
            $found_at = null;
            foreach ($search_locations as $location) {
                if (file_exists($location)) {
                    $found_at = $location;
                    echo "✅ Found file at: $location\n";
                    break;
                }
            }
            
            if ($found_at) {
                // Calculate correct storage_path
                $correct_storage_path = str_replace('../uploads/', '', $found_at);
                echo "Correct storage_path should be: $correct_storage_path\n";
                
                if ($correct_storage_path !== $file['storage_path']) {
                    echo "🔧 Updating database record...\n";
                    
                    $update_stmt = $pdo->prepare("
                        UPDATE documents 
                        SET storage_path = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    
                    if ($update_stmt->execute([$correct_storage_path, $file['id']])) {
                        echo "✅ Database updated successfully\n";
                        $fixed_count++;
                        
                        // Verify the fix
                        $new_admin_path = '../uploads/' . $correct_storage_path;
                        echo "Verification: File now expected at: $new_admin_path\n";
                        echo "Verification: File exists: " . (file_exists($new_admin_path) ? 'YES' : 'NO') . "\n";
                    } else {
                        echo "❌ Failed to update database\n";
                    }
                } else {
                    echo "ℹ️  Storage path is already correct, but file location issue\n";
                }
            } else {
                echo "❌ File not found anywhere!\n";
                $missing_files++;
                
                // List all files in uploads directory for debugging
                echo "Files in ../uploads/pdfs/:\n";
                if (is_dir('../uploads/pdfs/')) {
                    $files_in_dir = scandir('../uploads/pdfs/');
                    foreach ($files_in_dir as $f) {
                        if ($f !== '.' && $f !== '..') {
                            echo "  - $f\n";
                        }
                    }
                }
            }
        }
        echo "\n";
    }
    
    echo "=== SUMMARY ===\n";
    echo "Total files processed: " . count($files) . "\n";
    echo "Already correct: $already_correct\n";
    echo "Fixed: $fixed_count\n";
    echo "Missing files: $missing_files\n";
    
    if ($fixed_count > 0) {
        echo "\n✅ Fixed $fixed_count file path(s). Admin panel should now show correct status.\n";
    }
    
    if ($missing_files > 0) {
        echo "\n⚠️  $missing_files file(s) are missing from the filesystem.\n";
    }
    
    // Test admin panel file existence check
    echo "\n=== TESTING ADMIN PANEL LOGIC ===\n";
    $stmt = $pdo->query("SELECT * FROM documents ORDER BY created_at DESC LIMIT 1");
    $test_file = $stmt->fetch();
    
    if ($test_file) {
        echo "Testing most recent file: " . $test_file['original_name'] . "\n";
        $admin_path = '../uploads/' . $test_file['storage_path'];
        $exists = file_exists($admin_path);
        echo "Admin panel path: $admin_path\n";
        echo "File exists: " . ($exists ? 'YES' : 'NO') . "\n";
        echo "Status would show: " . ($exists ? 'Available' : 'Missing') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
