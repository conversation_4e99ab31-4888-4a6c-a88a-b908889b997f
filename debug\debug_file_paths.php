<?php
/**
 * Debug File Path Issues
 * Check exact paths and fix the mismatch between API and admin panel
 */

require_once '../api/config/config.php';

echo "<h1>File Path Debug</h1>\n";
echo "<pre>\n";

try {
    // Connect to database
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "=== CURRENT WORKING DIRECTORY ===\n";
    echo "Current directory: " . getcwd() . "\n";
    echo "Script location: " . __FILE__ . "\n\n";
    
    echo "=== CONFIGURATION VALUES ===\n";
    echo "UPLOAD_DIR constant: " . UPLOAD_DIR . "\n";
    echo "Resolved UPLOAD_DIR: " . realpath(UPLOAD_DIR) . "\n\n";
    
    // Get the most recent file
    $stmt = $pdo->query("
        SELECT * FROM documents 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    
    $file = $stmt->fetch();
    
    if (!$file) {
        echo "❌ No files found in database\n";
        exit();
    }
    
    echo "=== MOST RECENT FILE ANALYSIS ===\n";
    echo "File ID: " . $file['id'] . "\n";
    echo "Original Name: " . $file['original_name'] . "\n";
    echo "File Name: " . $file['file_name'] . "\n";
    echo "Storage Path (DB): " . $file['storage_path'] . "\n\n";
    
    echo "=== PATH TESTING FROM DEBUG DIRECTORY ===\n";
    $test_paths = [
        // From debug directory perspective
        '../uploads/' . $file['storage_path'],
        '../uploads/pdfs/' . $file['file_name'],
        '../uploads/' . $file['file_name'],
        
        // From admin directory perspective (where admin panel runs)
        '../uploads/' . $file['storage_path'],  // This is what admin panel uses
        
        // From API directory perspective
        UPLOAD_DIR . $file['storage_path'],
        UPLOAD_DIR . 'pdfs/' . $file['file_name'],
        
        // Absolute paths
        realpath('../uploads/') . '/' . $file['storage_path'],
        realpath('../uploads/pdfs/') . '/' . $file['file_name'],
    ];
    
    foreach ($test_paths as $path) {
        $exists = file_exists($path);
        $size = $exists ? filesize($path) : 0;
        $real_path = $exists ? realpath($path) : 'N/A';
        echo "Path: $path\n";
        echo "  Exists: " . ($exists ? '✅ YES' : '❌ NO') . "\n";
        echo "  Size: $size bytes\n";
        echo "  Real path: $real_path\n\n";
    }
    
    echo "=== DIRECTORY LISTING ===\n";
    $dirs_to_check = [
        '../uploads/',
        '../uploads/pdfs/',
        'uploads/',
        'uploads/pdfs/'
    ];
    
    foreach ($dirs_to_check as $dir) {
        echo "Directory: $dir\n";
        if (is_dir($dir)) {
            echo "  Exists: ✅ YES\n";
            echo "  Real path: " . realpath($dir) . "\n";
            $files = scandir($dir);
            echo "  Contents:\n";
            foreach ($files as $f) {
                if ($f !== '.' && $f !== '..') {
                    $full_path = $dir . $f;
                    $is_file = is_file($full_path);
                    $size = $is_file ? filesize($full_path) : 0;
                    echo "    - $f " . ($is_file ? "(file, $size bytes)" : "(directory)") . "\n";
                }
            }
        } else {
            echo "  Exists: ❌ NO\n";
        }
        echo "\n";
    }
    
    echo "=== ADMIN PANEL SIMULATION ===\n";
    // Simulate what admin panel does
    $admin_file_path = '../uploads/' . $file['storage_path'];
    echo "Admin panel checks: $admin_file_path\n";
    echo "File exists: " . (file_exists($admin_file_path) ? '✅ YES' : '❌ NO') . "\n";
    
    if (!file_exists($admin_file_path)) {
        echo "\n🔍 FINDING THE ACTUAL FILE LOCATION:\n";
        
        // Search for the file
        $search_dirs = ['../uploads/', '../uploads/pdfs/', 'uploads/', 'uploads/pdfs/'];
        foreach ($search_dirs as $search_dir) {
            if (is_dir($search_dir)) {
                $files = scandir($search_dir);
                foreach ($files as $f) {
                    if ($f === $file['file_name'] || $f === basename($file['storage_path'])) {
                        $found_path = $search_dir . $f;
                        echo "✅ FOUND FILE AT: $found_path\n";
                        echo "   Size: " . filesize($found_path) . " bytes\n";
                        echo "   Real path: " . realpath($found_path) . "\n";
                        
                        // Calculate what the storage_path should be
                        $correct_storage_path = str_replace('../uploads/', '', $found_path);
                        echo "   Correct storage_path should be: $correct_storage_path\n";
                        
                        if ($correct_storage_path !== $file['storage_path']) {
                            echo "   ⚠️  MISMATCH! Database has: " . $file['storage_path'] . "\n";
                            echo "   ⚠️  Should be: $correct_storage_path\n";
                        }
                    }
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
