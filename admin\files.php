<?php
/**
 * File Management
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'files';
$page_title = 'File Management';

// Handle file deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $file_id = $_POST['file_id'] ?? '';
    if ($file_id) {
        try {
            $pdo = getDbConnection();

            // Get file info from database
            $stmt = $pdo->prepare("SELECT storage_path, file_size, user_id FROM documents WHERE id = ?");
            $stmt->execute([$file_id]);
            $file_info = $stmt->fetch();

            if ($file_info) {
                // Delete file from database (hard delete since no deleted_at column)
                $stmt = $pdo->prepare("DELETE FROM documents WHERE id = ?");
                $stmt->execute([$file_id]);

                // Delete physical file
                $file_path = '../uploads/' . $file_info['storage_path'];
                if (file_exists($file_path)) {
                    unlink($file_path);
                }

                // Update storage usage
                $stmt = $pdo->prepare("
                    UPDATE storage_usage
                    SET total_used = GREATEST(0, total_used - ?),
                        document_count = GREATEST(0, document_count - 1),
                        last_updated = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$file_info['file_size'], $file_info['user_id']]);

                showAlert('File deleted successfully', 'success');
            } else {
                showAlert('File not found', 'warning');
            }
        } catch (Exception $e) {
            showAlert('Failed to delete file: ' . $e->getMessage(), 'danger');
            error_log("File deletion error: " . $e->getMessage());
        }
    }
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Get file list from database
$files = [];

try {
    $pdo = getDbConnection();

    // Get files from database with user information
    $stmt = $pdo->query("
        SELECT
            d.id,
            d.original_name,
            d.file_name,
            d.storage_path,
            d.file_size,
            d.file_type,
            d.created_at,
            d.download_count,
            u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.user_id = u.id
        ORDER BY d.created_at DESC
    ");

    $files = $stmt->fetchAll();

    // Add additional file information
    foreach ($files as &$file) {
        $file['extension'] = strtolower(pathinfo($file['original_name'], PATHINFO_EXTENSION));
        $file['file_path'] = '../uploads/' . $file['storage_path'];
        $file['exists'] = file_exists($file['file_path']);
        $file['modified'] = strtotime($file['created_at']);
    }

} catch (Exception $e) {
    error_log("Error fetching files: " . $e->getMessage());
    showAlert('Error loading files', 'danger');
}

include 'includes/header.php';
?>

<!-- File Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">File Management</h1>
        <p class="text-muted">Manage uploaded PDF files and documents</p>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="bi bi-cloud-upload me-2"></i>
            Upload File
        </button>
    </div>
</div>

<!-- File Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Files</h6>
                        <h3 class="mb-0"><?= count($files) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-files" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Size</h6>
                        <h3 class="mb-0"><?= formatFileSize(array_sum(array_column($files, 'size'))) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-hdd" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">PDF Files</h6>
                        <h3 class="mb-0"><?= count(array_filter($files, fn($f) => $f['extension'] === 'pdf')) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-file-earmark-pdf" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Images</h6>
                        <h3 class="mb-0"><?= count(array_filter($files, fn($f) => in_array($f['extension'], ['jpg', 'jpeg', 'png']))) ?></h3>
                    </div>
                    <div class="text-white-50">
                        <i class="bi bi-image" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-folder me-2"></i>
            Uploaded Files
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($files)): ?>
            <div class="text-center py-5">
                <i class="bi bi-cloud-upload text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No files uploaded</h4>
                <p class="text-muted">Upload your first file to get started.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="bi bi-cloud-upload me-2"></i>
                    Upload File
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>File</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Uploaded By</th>
                            <th>Upload Date</th>
                            <th>Downloads</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($files as $file): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-<?= getFileIcon($file['extension']) ?> me-2 text-primary"></i>
                                        <div>
                                            <div class="fw-medium"><?= htmlspecialchars($file['original_name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($file['file_type']) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= strtoupper($file['extension']) ?></span>
                                </td>
                                <td><?= formatFileSize($file['file_size']) ?></td>
                                <td>
                                    <small><?= htmlspecialchars($file['uploaded_by_name'] ?? 'Unknown') ?></small>
                                </td>
                                <td>
                                    <small><?= date('M j, Y g:i A', $file['modified']) ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= $file['download_count'] ?></span>
                                </td>
                                <td>
                                    <?php if ($file['exists']): ?>
                                        <span class="badge bg-success">Available</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Missing</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if ($file['exists']): ?>
                                            <a href="download.php?file=<?= urlencode($file['id']) ?>&action=view"
                                               class="btn btn-outline-primary" target="_blank" title="View">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="download.php?file=<?= urlencode($file['id']) ?>&action=download"
                                               class="btn btn-outline-success" title="Download">
                                                <i class="bi bi-download"></i>
                                            </a>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-outline-secondary" disabled title="File Missing">
                                                <i class="bi bi-eye-slash"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" disabled title="File Missing">
                                                <i class="bi bi-download"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="deleteFile('<?= htmlspecialchars($file['id']) ?>', '<?= htmlspecialchars($file['original_name']) ?>')" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" action="upload.php" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                        <div class="form-text">
                            Allowed types: PDF, JPG, PNG, DOC, DOCX. Maximum size: 50MB.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadFile()">
                    <i class="bi bi-cloud-upload me-2"></i>
                    Upload
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete form is now created dynamically in JavaScript -->

<?php
function getFileIcon($extension) {
    return match($extension) {
        'pdf' => 'file-earmark-pdf',
        'jpg', 'jpeg', 'png' => 'image',
        'doc', 'docx' => 'file-earmark-word',
        default => 'file-earmark'
    };
}

$extra_js = "
<script>
function deleteFile(fileId, fileName) {
    if (confirmDelete('Are you sure you want to delete \"' + fileName + '\"? This action cannot be undone.')) {
        // Create form dynamically since we need to pass file_id instead of filename
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\">' +
                        '<input type=\"hidden\" name=\"file_id\" value=\"' + fileId + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function uploadFile() {
    const form = document.getElementById('uploadForm');
    const fileInput = document.getElementById('file');
    
    if (!fileInput.files[0]) {
        showToast('Please select a file', 'warning');
        return;
    }
    
    const formData = new FormData(form);
    const uploadBtn = event.target;
    const hideLoading = showLoading(uploadBtn);
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast('File uploaded successfully', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.error?.message || 'Upload failed', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('Upload failed', 'danger');
        console.error('Error:', error);
    });
}
</script>
";

include 'includes/footer.php';
?>
