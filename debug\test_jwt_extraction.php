<?php
/**
 * JWT Token Extraction Test
 * Tests if JWT tokens are properly extracted from Authorization headers
 */

require_once '../api/config/config.php';
require_once '../api/utils/jwt.php';

// Set content type to JSON
header('Content-Type: application/json');

echo json_encode([
    'test' => 'JWT Token Extraction Test',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_info' => [
        'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'HTTP_AUTHORIZATION' => $_SERVER['HTTP_AUTHORIZATION'] ?? 'not set',
        'REDIRECT_HTTP_AUTHORIZATION' => $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] ?? 'not set'
    ],
    'headers' => function_exists('getallheaders') ? getallheaders() : 'getallheaders() not available',
    'apache_headers' => function_exists('apache_request_headers') ? apache_request_headers() : 'apache_request_headers() not available',
    'extracted_token' => JWT::getBearerToken(),
    'constants' => [
        'JWT_SECRET_KEY' => defined('JWT_SECRET_KEY') ? JWT_SECRET_KEY : 'not defined',
        'JWT_EXPIRATION_TIME' => defined('JWT_EXPIRATION_TIME') ? JWT_EXPIRATION_TIME : 'not defined'
    ]
], JSON_PRETTY_PRINT);
?>
