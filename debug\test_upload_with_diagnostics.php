<?php
/**
 * Test File Upload with Enhanced Diagnostics
 * Simulate the upload process to identify the root cause
 */

echo "<h1>File Upload Diagnostic Test</h1>\n";
echo "<pre>\n";

// Test 1: Authentication and get token
echo "=== STEP 1: AUTHENTICATION ===\n";
$login_url = 'http://192.168.0.106/MtcInvoiceMasudvi/api/auth/login.php';
$login_data = json_encode([
    'email' => '<EMAIL>',
    'password' => 'admin123'
]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $login_data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($login_data)
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$login_response = curl_exec($ch);
$login_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Login HTTP Code: $login_http_code\n";

$login_data = json_decode($login_response, true);
if ($login_http_code == 200 && isset($login_data['data']['token'])) {
    echo "✅ Authentication successful\n";
    $token = $login_data['data']['token'];
    $user_id = $login_data['data']['user']['id'];
    echo "User ID: $user_id\n";
} else {
    echo "❌ Authentication failed\n";
    echo "Response: $login_response\n";
    exit();
}

// Test 2: Create test file and upload
echo "\n=== STEP 2: FILE UPLOAD TEST ===\n";

// Create a test PDF file
$test_content = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF";
$test_file_name = 'diagnostic_test_' . time() . '.pdf';
$test_file_path = sys_get_temp_dir() . '/' . $test_file_name;
file_put_contents($test_file_path, $test_content);

echo "Created test file: $test_file_name\n";
echo "Test file path: $test_file_path\n";
echo "Test file size: " . filesize($test_file_path) . " bytes\n";
echo "Test file exists: " . (file_exists($test_file_path) ? 'YES' : 'NO') . "\n";

// Clear PHP error log before upload
$error_log_path = ini_get('error_log');
echo "PHP error log: $error_log_path\n";

// Upload the file with detailed monitoring
$upload_url = 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/upload.php';

echo "\nUploading file...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $upload_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, [
    'file' => new CURLFile($test_file_path, 'application/pdf', $test_file_name)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$upload_response = curl_exec($ch);
$upload_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$upload_info = curl_getinfo($ch);
curl_close($ch);

echo "Upload HTTP Code: $upload_http_code\n";
echo "Upload response length: " . strlen($upload_response) . " bytes\n";

// Separate headers and body
$header_size = $upload_info['header_size'];
$response_headers = substr($upload_response, 0, $header_size);
$response_body = substr($upload_response, $header_size);

echo "\nResponse headers:\n";
echo $response_headers . "\n";

echo "Response body:\n";
echo $response_body . "\n";

// Parse JSON response
$upload_data = json_decode($response_body, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Response is valid JSON\n";
    
    if ($upload_http_code == 200 && isset($upload_data['success']) && $upload_data['success']) {
        echo "✅ Upload API reported success\n";
        $uploaded_file = $upload_data['data'];
        echo "File ID: " . $uploaded_file['id'] . "\n";
        echo "Stored name: " . $uploaded_file['stored_name'] . "\n";
        echo "Original name: " . $uploaded_file['original_name'] . "\n";
        echo "Relative path: " . $uploaded_file['relative_path'] . "\n";
        
        // Test 3: Verify database record
        echo "\n=== STEP 3: DATABASE VERIFICATION ===\n";
        
        try {
            require_once '../api/config/config.php';
            require_once '../api/config/database.php';
            
            $database = new Database();
            $pdo = $database->getConnection();
            
            if ($pdo) {
                $stmt = $pdo->prepare("SELECT * FROM documents WHERE id = ?");
                $stmt->execute([$uploaded_file['id']]);
                $db_record = $stmt->fetch();
                
                if ($db_record) {
                    echo "✅ Database record found\n";
                    echo "DB ID: " . $db_record['id'] . "\n";
                    echo "DB original_name: " . $db_record['original_name'] . "\n";
                    echo "DB file_name: " . $db_record['file_name'] . "\n";
                    echo "DB storage_path: " . $db_record['storage_path'] . "\n";
                    echo "DB file_size: " . $db_record['file_size'] . "\n";
                } else {
                    echo "❌ Database record not found\n";
                }
            }
        } catch (Exception $e) {
            echo "❌ Database error: " . $e->getMessage() . "\n";
        }
        
        // Test 4: Verify physical file
        echo "\n=== STEP 4: PHYSICAL FILE VERIFICATION ===\n";
        
        $expected_paths = [
            '../uploads/' . $uploaded_file['relative_path'],
            '../uploads/pdfs/' . $uploaded_file['stored_name'],
            'uploads/' . $uploaded_file['relative_path'],
            'uploads/pdfs/' . $uploaded_file['stored_name']
        ];
        
        $file_found = false;
        foreach ($expected_paths as $path) {
            $exists = file_exists($path);
            echo "Path: $path - " . ($exists ? '✅ EXISTS' : '❌ MISSING') . "\n";
            if ($exists) {
                echo "  Size: " . filesize($path) . " bytes\n";
                echo "  Modified: " . date('Y-m-d H:i:s', filemtime($path)) . "\n";
                $file_found = true;
            }
        }
        
        if (!$file_found) {
            echo "\n❌ CRITICAL: Physical file not found anywhere!\n";
            
            // Check what's in the uploads directory
            echo "\nContents of uploads directories:\n";
            $dirs_to_check = ['../uploads/', '../uploads/pdfs/', 'uploads/', 'uploads/pdfs/'];
            foreach ($dirs_to_check as $dir) {
                if (is_dir($dir)) {
                    echo "$dir:\n";
                    $files = scandir($dir);
                    foreach ($files as $file) {
                        if ($file !== '.' && $file !== '..') {
                            $size = is_file($dir . $file) ? filesize($dir . $file) : 0;
                            echo "  - $file ($size bytes)\n";
                        }
                    }
                } else {
                    echo "$dir: Directory not found\n";
                }
            }
        }
        
    } else {
        echo "❌ Upload API reported failure\n";
        echo "Error: " . ($upload_data['error']['message'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ Response is not valid JSON\n";
    echo "JSON Error: " . json_last_error_msg() . "\n";
}

// Test 5: Check PHP error log
echo "\n=== STEP 5: PHP ERROR LOG ANALYSIS ===\n";

if ($error_log_path && file_exists($error_log_path)) {
    echo "Reading recent error log entries...\n";
    $log_content = file_get_contents($error_log_path);
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -50); // Last 50 lines
    
    $upload_related = [];
    foreach ($recent_lines as $line) {
        if (strpos($line, 'FILE UPLOAD') !== false || 
            strpos($line, 'move_uploaded_file') !== false ||
            strpos($line, 'upload') !== false) {
            $upload_related[] = $line;
        }
    }
    
    if (!empty($upload_related)) {
        echo "Upload-related log entries:\n";
        foreach ($upload_related as $line) {
            echo "  $line\n";
        }
    } else {
        echo "No upload-related entries found in recent logs\n";
    }
} else {
    echo "Error log not accessible or doesn't exist\n";
}

// Cleanup
unlink($test_file_path);

echo "\n=== DIAGNOSTIC COMPLETE ===\n";
echo "</pre>";
?>
