<?php
/**
 * Simple Authentication Test
 */

echo "=== SIMPLE AUTH TEST ===\n";

// Test 1: Check if files exist
echo "1. Checking required files...\n";
$files = [
    'api/config/config.php',
    'api/config/database.php', 
    'api/utils/jwt.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists\n";
    } else {
        echo "❌ $file missing\n";
    }
}

// Test 2: Include files
echo "\n2. Including files...\n";
try {
    require_once 'api/config/config.php';
    echo "✅ config.php included\n";
    
    require_once 'api/config/database.php';
    echo "✅ database.php included\n";
    
    require_once 'api/utils/jwt.php';
    echo "✅ jwt.php included\n";
} catch (Exception $e) {
    echo "❌ Include error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 3: Database connection
echo "\n3. Testing database connection...\n";
try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    if ($pdo) {
        echo "✅ Database connected\n";
    } else {
        echo "❌ Database connection failed\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 4: JWT constants
echo "\n4. Checking JWT constants...\n";
if (defined('JWT_SECRET_KEY')) {
    echo "✅ JWT_SECRET_KEY: " . JWT_SECRET_KEY . "\n";
} else {
    echo "❌ JWT_SECRET_KEY not defined\n";
}

if (defined('JWT_EXPIRATION_TIME')) {
    echo "✅ JWT_EXPIRATION_TIME: " . JWT_EXPIRATION_TIME . "\n";
} else {
    echo "❌ JWT_EXPIRATION_TIME not defined\n";
}

// Test 5: Test API endpoint directly
echo "\n5. Testing API endpoint...\n";
$api_url = 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/list';

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $api_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_HEADER => true
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $http_code\n";
echo "Response: " . substr($response, 0, 200) . "...\n";

echo "\n=== TEST COMPLETE ===\n";
