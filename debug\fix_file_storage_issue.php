<?php
/**
 * Comprehensive File Storage Issue Fix
 * Addresses the most common causes of file upload failures
 */

require_once '../api/config/config.php';

echo "<h1>File Storage Issue Fix</h1>\n";
echo "<pre>\n";

$fixes_applied = [];
$errors = [];

echo "=== ANALYZING CURRENT SITUATION ===\n";

// Check if directories exist and create them if needed
$upload_dir = UPLOAD_DIR . 'pdfs/';
echo "Target upload directory: $upload_dir\n";
echo "Absolute path: " . realpath(dirname($upload_dir)) . "\n";

// Fix 1: Ensure directories exist with proper permissions
echo "\n=== FIX 1: DIRECTORY CREATION AND PERMISSIONS ===\n";

$directories_to_fix = [
    '../uploads/',
    '../uploads/pdfs/',
    UPLOAD_DIR,
    UPLOAD_DIR . 'pdfs/'
];

foreach ($directories_to_fix as $dir) {
    echo "Processing directory: $dir\n";
    
    if (!is_dir($dir)) {
        echo "  Creating directory...\n";
        if (mkdir($dir, 0777, true)) {
            echo "  ✅ Directory created\n";
            $fixes_applied[] = "Created directory: $dir";
        } else {
            echo "  ❌ Failed to create directory\n";
            $errors[] = "Failed to create directory: $dir";
            continue;
        }
    }
    
    // Set permissions
    if (chmod($dir, 0777)) {
        echo "  ✅ Permissions set to 777\n";
        $fixes_applied[] = "Set permissions for: $dir";
    } else {
        echo "  ⚠️  Could not change permissions\n";
        $errors[] = "Could not change permissions for: $dir";
    }
    
    // Test write access
    $test_file = $dir . 'test_' . time() . '.tmp';
    if (file_put_contents($test_file, 'test')) {
        echo "  ✅ Write test successful\n";
        unlink($test_file);
    } else {
        echo "  ❌ Write test failed\n";
        $errors[] = "Write test failed for: $dir";
    }
}

// Fix 2: Check and fix upload.php path resolution
echo "\n=== FIX 2: UPLOAD.PHP PATH RESOLUTION ===\n";

$upload_php_path = '../api/files/upload.php';
if (file_exists($upload_php_path)) {
    echo "✅ upload.php exists\n";
    
    // Check if the UPLOAD_DIR constant resolves correctly from upload.php perspective
    $api_working_dir = dirname(realpath($upload_php_path));
    echo "API working directory: $api_working_dir\n";
    
    $resolved_upload_dir = $api_working_dir . '/' . UPLOAD_DIR . 'pdfs/';
    echo "Resolved upload directory from API: $resolved_upload_dir\n";
    
    if (!is_dir($resolved_upload_dir)) {
        echo "Creating resolved upload directory...\n";
        if (mkdir($resolved_upload_dir, 0777, true)) {
            echo "✅ Resolved directory created\n";
            $fixes_applied[] = "Created resolved upload directory";
        } else {
            echo "❌ Failed to create resolved directory\n";
            $errors[] = "Failed to create resolved upload directory";
        }
    }
} else {
    echo "❌ upload.php not found\n";
    $errors[] = "upload.php file not found";
}

// Fix 3: Update upload.php to use absolute paths
echo "\n=== FIX 3: ABSOLUTE PATH IMPLEMENTATION ===\n";

$absolute_upload_dir = realpath('../uploads/pdfs/');
if ($absolute_upload_dir) {
    echo "Absolute upload directory: $absolute_upload_dir\n";
    
    // Create a backup of upload.php
    $backup_path = '../api/files/upload.php.backup.' . time();
    if (copy('../api/files/upload.php', $backup_path)) {
        echo "✅ Backup created: $backup_path\n";
        
        // Read current upload.php
        $upload_content = file_get_contents('../api/files/upload.php');
        
        // Replace relative path with absolute path
        $old_pattern = "UPLOAD_DIR . 'pdfs/'";
        $new_pattern = "'" . addslashes($absolute_upload_dir) . "/'";
        
        if (strpos($upload_content, $old_pattern) !== false) {
            $new_content = str_replace($old_pattern, $new_pattern, $upload_content);
            
            if (file_put_contents('../api/files/upload.php', $new_content)) {
                echo "✅ upload.php updated with absolute path\n";
                $fixes_applied[] = "Updated upload.php with absolute path";
            } else {
                echo "❌ Failed to update upload.php\n";
                $errors[] = "Failed to update upload.php";
            }
        } else {
            echo "ℹ️  Pattern not found in upload.php (may already be fixed)\n";
        }
    } else {
        echo "❌ Failed to create backup\n";
        $errors[] = "Failed to create backup of upload.php";
    }
} else {
    echo "❌ Could not resolve absolute path\n";
    $errors[] = "Could not resolve absolute upload path";
}

// Fix 4: Test the upload process
echo "\n=== FIX 4: UPLOAD PROCESS TEST ===\n";

// Create a test file
$test_content = "Test file created at " . date('Y-m-d H:i:s');
$test_file_path = sys_get_temp_dir() . '/fix_test_' . time() . '.txt';
file_put_contents($test_file_path, $test_content);

echo "Created test file: $test_file_path\n";

// Simulate move_uploaded_file
$target_path = '../uploads/pdfs/fix_test_' . time() . '.txt';
echo "Target path: $target_path\n";

if (copy($test_file_path, $target_path)) {
    echo "✅ File copy successful\n";
    echo "File exists: " . (file_exists($target_path) ? 'YES' : 'NO') . "\n";
    echo "File size: " . filesize($target_path) . " bytes\n";
    
    // Clean up test file
    unlink($target_path);
    $fixes_applied[] = "File copy test successful";
} else {
    echo "❌ File copy failed\n";
    $errors[] = "File copy test failed";
}

// Clean up temp file
unlink($test_file_path);

// Fix 5: Database cleanup for orphaned records
echo "\n=== FIX 5: DATABASE CLEANUP ===\n";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    // Find orphaned records (database entries without physical files)
    $stmt = $pdo->query("
        SELECT id, original_name, file_name, storage_path, file_size 
        FROM documents 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    
    $orphaned_count = 0;
    $files = $stmt->fetchAll();
    
    foreach ($files as $file) {
        $file_path = '../uploads/' . $file['storage_path'];
        if (!file_exists($file_path)) {
            $orphaned_count++;
            echo "Orphaned record: " . $file['original_name'] . " (ID: " . $file['id'] . ")\n";
        }
    }
    
    echo "Found $orphaned_count orphaned database records\n";
    
    if ($orphaned_count > 0) {
        echo "Note: You may want to clean up orphaned records manually\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    $errors[] = "Database error: " . $e->getMessage();
}

// Summary
echo "\n=== SUMMARY ===\n";
echo "Fixes applied: " . count($fixes_applied) . "\n";
foreach ($fixes_applied as $fix) {
    echo "  ✅ $fix\n";
}

echo "\nErrors encountered: " . count($errors) . "\n";
foreach ($errors as $error) {
    echo "  ❌ $error\n";
}

echo "\n=== NEXT STEPS ===\n";
echo "1. Test file upload from Android app again\n";
echo "2. Check the diagnostic upload test: debug/test_upload_with_diagnostics.php\n";
echo "3. Monitor PHP error logs for any remaining issues\n";
echo "4. If issues persist, check web server configuration\n";

if (count($fixes_applied) > 0) {
    echo "\n✅ Fixes have been applied. Please test the upload functionality now.\n";
} else {
    echo "\n⚠️  No fixes could be applied. Manual intervention may be required.\n";
}

echo "</pre>";
?>
