<?php
/**
 * Documents Table Migration Script
 * Safely migrates documents table from UUID to AUTO_INCREMENT
 */

require_once 'api/config/config.php';
require_once 'api/config/database.php';

echo "=== DOCUMENTS TABLE MIGRATION ===\n\n";

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    if (!$pdo) {
        throw new Exception("Database connection failed");
    }
    
    echo "✅ Database connected\n\n";
    
    // Step 1: Check current table structure
    echo "Step 1: Checking current table structure...\n";
    $stmt = $pdo->query("DESCRIBE documents");
    $columns = $stmt->fetchAll();
    
    $id_column = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'id') {
            $id_column = $column;
            break;
        }
    }
    
    if (!$id_column) {
        throw new Exception("Documents table not found or missing ID column");
    }
    
    echo "Current ID column type: {$id_column['Type']}\n";
    
    if (strpos(strtolower($id_column['Type']), 'char') !== false) {
        echo "⚠️  ID column is using CHAR (UUID) - migration needed\n\n";
    } else {
        echo "✅ ID column is already using proper integer type - no migration needed\n";
        exit(0);
    }
    
    // Step 2: Count existing records
    echo "Step 2: Counting existing records...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM documents");
    $count = $stmt->fetch()['count'];
    echo "Found $count existing documents\n\n";
    
    // Step 3: Create backup
    echo "Step 3: Creating backup table...\n";
    $pdo->exec("DROP TABLE IF EXISTS documents_backup");
    $pdo->exec("CREATE TABLE documents_backup AS SELECT * FROM documents");
    echo "✅ Backup created\n\n";
    
    // Step 4: Create new table structure
    echo "Step 4: Creating new table structure...\n";
    $pdo->exec("DROP TABLE IF EXISTS documents_new");
    
    $create_sql = "
    CREATE TABLE `documents_new` (
      `id` INT(11) NOT NULL AUTO_INCREMENT,
      `uuid` CHAR(36) NOT NULL,
      `user_id` INT(11) NOT NULL,
      `original_name` VARCHAR(255) NOT NULL,
      `storage_path` VARCHAR(512) NOT NULL,
      `file_name` VARCHAR(255) NOT NULL,
      `file_size` BIGINT NOT NULL DEFAULT 0,
      `file_type` VARCHAR(100) NOT NULL,
      `file_hash` VARCHAR(64) NOT NULL,
      `description` TEXT,
      `download_count` INT DEFAULT 0,
      `last_downloaded_at` DATETIME DEFAULT NULL,
      `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `idx_uuid` (`uuid`),
      UNIQUE KEY `idx_file_hash_user` (`file_hash`, `user_id`),
      KEY `idx_user` (`user_id`),
      KEY `idx_created_at` (`created_at`),
      FULLTEXT KEY `ft_search` (`original_name`, `description`),
      FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($create_sql);
    echo "✅ New table structure created\n\n";
    
    // Step 5: Migrate data
    echo "Step 5: Migrating existing data...\n";
    
    if ($count > 0) {
        $migrate_sql = "
        INSERT INTO `documents_new` (
          `uuid`, 
          `user_id`, 
          `original_name`, 
          `storage_path`, 
          `file_name`, 
          `file_size`, 
          `file_type`, 
          `file_hash`, 
          `description`, 
          `download_count`, 
          `last_downloaded_at`, 
          `created_at`, 
          `updated_at`
        )
        SELECT 
          `id` as `uuid`,
          `user_id`, 
          `original_name`, 
          `storage_path`, 
          `file_name`, 
          COALESCE(`file_size`, 0) as `file_size`,
          `file_type`, 
          `file_hash`, 
          `description`, 
          COALESCE(`download_count`, 0) as `download_count`,
          `last_downloaded_at`, 
          `created_at`, 
          `updated_at`
        FROM `documents`";
        
        $pdo->exec($migrate_sql);
        
        // Verify migration
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM documents_new");
        $new_count = $stmt->fetch()['count'];
        
        if ($new_count !== $count) {
            throw new Exception("Migration failed: Expected $count records, got $new_count");
        }
        
        echo "✅ Migrated $new_count records\n\n";
    } else {
        echo "ℹ️  No data to migrate\n\n";
    }
    
    // Step 6: Replace tables
    echo "Step 6: Replacing old table with new one...\n";
    $pdo->exec("DROP TABLE documents");
    $pdo->exec("RENAME TABLE documents_new TO documents");
    echo "✅ Table replacement complete\n\n";
    
    // Step 7: Verify final structure
    echo "Step 7: Verifying new structure...\n";
    $stmt = $pdo->query("DESCRIBE documents");
    $new_columns = $stmt->fetchAll();
    
    echo "New table structure:\n";
    foreach ($new_columns as $column) {
        echo "  - {$column['Field']}: {$column['Type']} ({$column['Key']})\n";
    }
    
    // Check data integrity
    $stmt = $pdo->query("SELECT COUNT(*) as total, MIN(id) as min_id, MAX(id) as max_id FROM documents");
    $stats = $stmt->fetch();
    
    echo "\nData verification:\n";
    echo "  - Total records: {$stats['total']}\n";
    echo "  - ID range: {$stats['min_id']} - {$stats['max_id']}\n";
    
    echo "\n🎉 MIGRATION COMPLETED SUCCESSFULLY!\n";
    echo "\nChanges made:\n";
    echo "- ID field changed from CHAR(36) to INT AUTO_INCREMENT\n";
    echo "- Added UUID field to preserve old IDs\n";
    echo "- Added proper file_size default value\n";
    echo "- Optimized indexes for better performance\n";
    echo "\nBackup table 'documents_backup' contains original data\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "\nRolling back changes...\n";
    
    try {
        // Attempt rollback
        if (isset($pdo)) {
            $pdo->exec("DROP TABLE IF EXISTS documents_new");
            
            // Check if backup exists and restore if needed
            $stmt = $pdo->query("SHOW TABLES LIKE 'documents_backup'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->query("SHOW TABLES LIKE 'documents'");
                if ($stmt->rowCount() === 0) {
                    $pdo->exec("CREATE TABLE documents AS SELECT * FROM documents_backup");
                    echo "✅ Restored from backup\n";
                }
            }
        }
    } catch (Exception $rollback_error) {
        echo "❌ Rollback failed: " . $rollback_error->getMessage() . "\n";
    }
    
    exit(1);
}
?>
