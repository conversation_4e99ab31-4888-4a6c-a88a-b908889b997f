1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.invoicegenarator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:7:5-9:40
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:8:9-66
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Need this for API 33 -->
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:5-80
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:22-77
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:5-76
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:22-73
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b2295aa26631e50da367b65abecee9c6\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
18
19    <permission
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
20        android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-71:19
26        android:allowBackup="true"
26-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:14:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\49be09e8971baf73797a99d4836d4a12\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:15:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:16:9-54
32        android:icon="@mipmap/ic_launcher"
32-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:17:9-43
33        android:label="@string/app_name"
33-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:18:9-41
34        android:networkSecurityConfig="@xml/network_security_config"
34-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:19:9-69
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:20:9-54
36        android:supportsRtl="true"
36-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:21:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.NewInvoice" >
38-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:22:9-48
39        <activity
39-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:24:9-26:40
40            android:name="com.official.invoicegenarator.InvoiceTwo"
40-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:25:13-39
41            android:exported="false" />
41-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:26:13-37
42        <activity
42-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:27:9-29:40
43            android:name="com.official.invoicegenarator.InvoiceTraker"
43-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:28:13-42
44            android:exported="false" />
44-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:29:13-37
45        <activity
45-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:30:9-32:40
46            android:name="com.official.invoicegenarator.PdfViewerActivity"
46-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:31:13-46
47            android:exported="false" />
47-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:32:13-37
48        <activity
48-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:33:9-35:40
49            android:name="com.official.invoicegenarator.DownloadListActivity"
49-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:34:13-49
50            android:exported="false" />
50-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:35:13-37
51        <activity
51-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:36:9-38:40
52            android:name="com.official.invoicegenarator.MoneyBagActivity"
52-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:37:13-45
53            android:exported="false" />
53-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:38:13-37
54        <activity
54-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:39:9-41:40
55            android:name="com.official.invoicegenarator.ProfileActivity"
55-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:40:13-44
56            android:exported="false" />
56-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:41:13-37
57        <activity
57-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:42:9-44:40
58            android:name="com.official.invoicegenarator.FingerprintSettingsActivity"
58-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:43:13-56
59            android:exported="false" />
59-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:44:13-37
60        <activity
60-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:45:9-47:40
61            android:name="com.official.invoicegenarator.VarifyActivity"
61-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:46:13-43
62            android:exported="false" />
62-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:47:13-37
63        <activity
63-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:48:9-50:40
64            android:name="com.official.invoicegenarator.WorkerAttendenceActivity"
64-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:49:13-53
65            android:exported="false" />
65-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:50:13-37
66        <activity
66-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:51:9-54:58
67            android:name="com.official.invoicegenarator.LoginActivity"
67-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:52:13-42
68            android:exported="false"
68-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:53:13-37
69            android:windowSoftInputMode="adjustResize" />
69-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:54:13-55
70        <activity
70-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:55:9-57:40
71            android:name="com.official.invoicegenarator.SelectionActivity"
71-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:56:13-46
72            android:exported="false" />
72-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:57:13-37
73        <activity
73-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:58:9-61:55
74            android:name="com.official.invoicegenarator.Home"
74-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:13-33
75            android:exported="false"
75-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:60:13-37
76            android:windowSoftInputMode="adjustPan" />
76-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:13-52
77        <activity
77-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:62:9-70:20
78            android:name="com.official.invoicegenarator.MainActivity"
78-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:63:13-41
79            android:exported="true" >
79-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:64:13-36
80            <intent-filter>
80-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:65:13-69:29
81                <action android:name="android.intent.action.MAIN" />
81-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:66:17-69
81-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:66:25-66
82
83                <category android:name="android.intent.category.LAUNCHER" />
83-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:68:17-77
83-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:68:27-74
84            </intent-filter>
85        </activity>
86        <activity
86-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
87            android:name="com.karumi.dexter.DexterActivity"
87-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
88            android:theme="@style/Dexter.Internal.Theme.Transparent" />
88-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c71d9f0e23bec21d0069612a6f883605\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
89
90        <provider
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
91            android:name="androidx.startup.InitializationProvider"
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
92            android:authorities="com.official.invoicegenarator.androidx-startup"
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
93            android:exported="false" >
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
94            <meta-data
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.emoji2.text.EmojiCompatInitializer"
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
96                android:value="androidx.startup" />
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5c93d2c11bd120b0016a88f3246f6997\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
98-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
99                android:value="androidx.startup" />
99-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0a877312e955051efbbfab36882c2c03\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
102                android:value="androidx.startup" />
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
103        </provider>
104
105        <receiver
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
106            android:name="androidx.profileinstaller.ProfileInstallReceiver"
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
107            android:directBootAware="false"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
108            android:enabled="true"
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
109            android:exported="true"
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
110            android:permission="android.permission.DUMP" >
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
112                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
115                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
116            </intent-filter>
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
118                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
119            </intent-filter>
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
121                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2eeba7026a587557c0dd499d0be4a42\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
122            </intent-filter>
123        </receiver>
124    </application>
125
126</manifest>
