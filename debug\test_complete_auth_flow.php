<?php
/**
 * Complete Authentication Flow Test
 * Tests the entire authentication flow from login to API access
 */

echo "=== COMPLETE AUTHENTICATION FLOW TEST ===\n\n";

// Step 1: Login to get JWT token
echo "Step 1: Getting JWT token via login...\n";

$login_data = json_encode([
    'email' => '<EMAIL>',
    'password' => 'admin123'
]);

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'http://192.168.0.106/MtcInvoiceMasudvi/api/auth/login',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $login_data,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($login_data)
    ],
    CURLOPT_TIMEOUT => 10
]);

$login_response = curl_exec($ch);
$login_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$login_error = curl_error($ch);
curl_close($ch);

echo "Login HTTP Code: $login_http_code\n";
if ($login_error) {
    echo "Login cURL Error: $login_error\n";
}

if ($login_http_code !== 200) {
    echo "❌ Login failed\n";
    echo "Response: $login_response\n";
    exit(1);
}

$login_data = json_decode($login_response, true);
if (!$login_data || !isset($login_data['data']['token'])) {
    echo "❌ Invalid login response format\n";
    echo "Response: $login_response\n";
    exit(1);
}

$token = $login_data['data']['token'];
echo "✅ Login successful\n";
echo "Token: " . substr($token, 0, 50) . "...\n";
echo "User: " . $login_data['data']['user']['name'] . " (" . $login_data['data']['user']['email'] . ")\n\n";

// Step 2: Test file list endpoint with token
echo "Step 2: Testing file list endpoint with JWT token...\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/list',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json'
    ],
    CURLOPT_TIMEOUT => 10
]);

$files_response = curl_exec($ch);
$files_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$files_error = curl_error($ch);
curl_close($ch);

echo "Files API HTTP Code: $files_http_code\n";
if ($files_error) {
    echo "Files API cURL Error: $files_error\n";
}

if ($files_http_code === 200) {
    echo "✅ File list API call successful\n";
    $files_data = json_decode($files_response, true);
    if ($files_data && isset($files_data['data']['files'])) {
        $file_count = count($files_data['data']['files']);
        echo "Found $file_count files\n";
        if ($file_count > 0) {
            echo "Sample files:\n";
            foreach (array_slice($files_data['data']['files'], 0, 3) as $file) {
                echo "  - {$file['original_name']} ({$file['file_size']} bytes)\n";
            }
        }
    }
} else {
    echo "❌ File list API call failed\n";
    echo "Response: $files_response\n";
}

echo "\n";

// Step 3: Test without token (should fail)
echo "Step 3: Testing file list endpoint without token (should fail)...\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/list',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json'
    ],
    CURLOPT_TIMEOUT => 10
]);

$no_auth_response = curl_exec($ch);
$no_auth_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "No Auth HTTP Code: $no_auth_http_code\n";
if ($no_auth_http_code === 401) {
    echo "✅ Correctly rejected request without token\n";
} else {
    echo "⚠️  Unexpected response for request without token\n";
    echo "Response: $no_auth_response\n";
}

echo "\n";

// Step 4: Test with invalid token (should fail)
echo "Step 4: Testing file list endpoint with invalid token (should fail)...\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/list',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Authorization: Bearer invalid-token-123',
        'Content-Type: application/json'
    ],
    CURLOPT_TIMEOUT => 10
]);

$invalid_auth_response = curl_exec($ch);
$invalid_auth_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Invalid Auth HTTP Code: $invalid_auth_http_code\n";
if ($invalid_auth_http_code === 401) {
    echo "✅ Correctly rejected request with invalid token\n";
} else {
    echo "⚠️  Unexpected response for request with invalid token\n";
    echo "Response: $invalid_auth_response\n";
}

echo "\n=== TEST SUMMARY ===\n";
echo "1. Login: " . ($login_http_code === 200 ? "✅ SUCCESS" : "❌ FAILED") . "\n";
echo "2. File List (with token): " . ($files_http_code === 200 ? "✅ SUCCESS" : "❌ FAILED") . "\n";
echo "3. File List (no token): " . ($no_auth_http_code === 401 ? "✅ SUCCESS" : "⚠️  UNEXPECTED") . "\n";
echo "4. File List (invalid token): " . ($invalid_auth_http_code === 401 ? "✅ SUCCESS" : "⚠️  UNEXPECTED") . "\n";

if ($login_http_code === 200 && $files_http_code === 200 && $no_auth_http_code === 401 && $invalid_auth_http_code === 401) {
    echo "\n🎉 ALL TESTS PASSED - Authentication is working correctly!\n";
} else {
    echo "\n⚠️  Some tests failed - check the details above\n";
}
?>
